{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\r\nimport type { NextRequest } from 'next/server';\r\n\r\n/**\r\n * Check if a JWT token is expired\r\n * @param token - JWT token to check\r\n * @returns true if token is expired, false otherwise\r\n */\r\nconst isTokenExpired = (token: string): boolean => {\r\n  if (!token) return true;\r\n\r\n  try {\r\n    // Decode JWT payload (without verification - just for expiry check)\r\n    const payload = JSON.parse(atob(token.split('.')[1]));\r\n    const currentTime = Math.floor(Date.now() / 1000);\r\n\r\n    // Check if token has expired\r\n    return payload.exp < currentTime;\r\n  } catch (error) {\r\n    console.error('Error decoding token:', error);\r\n    return true; // Treat invalid tokens as expired\r\n  }\r\n};\r\n\r\n/**\r\n * Check if user is authorized for admin/staff routes\r\n * @param user - User object with email and roles\r\n * @returns true if user is authorized for admin routes, false otherwise\r\n */\r\nconst isAuthorizedForAdminRoutes = (user: any): boolean => {\r\n  if (!user || !user.roles) return false;\r\n\r\n  // Check if user has staff role\r\n  const hasStaffRole = user.roles && !user.isCustomer;\r\n\r\n  return hasStaffRole;\r\n};\r\n\r\nexport function middleware(request: NextRequest) {\r\n  const url = request.nextUrl.clone();\r\n\r\n  // Get auth tokens from cookies\r\n  const authToken = request.cookies.get('auth_token');\r\n  const authUser = request.cookies.get('auth_user');\r\n\r\n  // Parse user data if available\r\n  let user = null;\r\n  let token = null;\r\n\r\n  // Try to get user auth\r\n  if (authUser) {\r\n    try {\r\n      user = JSON.parse(authUser.value);\r\n    } catch (error) {\r\n      console.error('Failed to parse user data:', error);\r\n    }\r\n  }\r\n\r\n  // Try to get token auth\r\n  if (authToken) {\r\n    try {\r\n      token = JSON.parse(authToken.value);\r\n    } catch (error) {\r\n      console.error('Failed to parse token data:', error);\r\n    }\r\n  }\r\n\r\n\r\n  // Always allow auth routes and public routes without redirection\r\n  if (url.pathname.startsWith('/customer/auth/') ||\r\n      url.pathname.startsWith('/auth/') ||\r\n      url.pathname.startsWith('/documentations') ||\r\n      url.pathname.startsWith('/public/')) {\r\n    return NextResponse.next();\r\n  }\r\n  \r\n  // Token Expired - redirection\r\n  if (isTokenExpired(token ?? '') || !token || !user) {\r\n    if (url.pathname.startsWith('/customer')) {\r\n      url.pathname = '/customer/auth/login';\r\n    } else {\r\n      url.pathname = '/auth/login';\r\n    }\r\n    return NextResponse.redirect(url);\r\n  }\r\n\r\n  // Handle root path redirections\r\n  if (!user.isCustomer && !url.pathname.startsWith('/customer')) {\r\n    return NextResponse.next();\r\n  } \r\n\r\n  if (user.isCustomer && url.pathname.startsWith('/customer')) {\r\n    return NextResponse.next();\r\n  } \r\n  // If already on auth page, allow through to prevent redirect loops\r\n   \r\n  // No valid url found - redirection to login\r\n  if (url.pathname.startsWith('/customer')) {\r\n    url.pathname = '/customer/auth/login';\r\n  } else {\r\n    url.pathname = '/auth/login';\r\n  }\r\n  return NextResponse.redirect(url);\r\n\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\r\n    /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api (API routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - images (public images)\r\n     */\r\n    '/((?!api|_next/static|_next/image|favicon.ico|images).*)',\r\n  ],\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGA;;;;CAIC,GACD,MAAM,iBAAiB,CAAC;IACtB,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI;QACF,oEAAoE;QACpE,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QAE5C,6BAA6B;QAC7B,OAAO,QAAQ,GAAG,GAAG;IACvB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,MAAM,kCAAkC;IACjD;AACF;AAEA;;;;CAIC,GACD,MAAM,6BAA6B,CAAC;IAClC,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE,OAAO;IAEjC,+BAA+B;IAC/B,MAAM,eAAe,KAAK,KAAK,IAAI,CAAC,KAAK,UAAU;IAEnD,OAAO;AACT;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;IAEjC,+BAA+B;IAC/B,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;IACtC,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC;IAErC,+BAA+B;IAC/B,IAAI,OAAO;IACX,IAAI,QAAQ;IAEZ,uBAAuB;IACvB,IAAI,UAAU;QACZ,IAAI;YACF,OAAO,KAAK,KAAK,CAAC,SAAS,KAAK;QAClC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,wBAAwB;IACxB,IAAI,WAAW;QACb,IAAI;YACF,QAAQ,KAAK,KAAK,CAAC,UAAU,KAAK;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAGA,iEAAiE;IACjE,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,sBACxB,IAAI,QAAQ,CAAC,UAAU,CAAC,aACxB,IAAI,QAAQ,CAAC,UAAU,CAAC,sBACxB,IAAI,QAAQ,CAAC,UAAU,CAAC,aAAa;QACvC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,8BAA8B;IAC9B,IAAI,eAAe,SAAS,OAAO,CAAC,SAAS,CAAC,MAAM;QAClD,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,cAAc;YACxC,IAAI,QAAQ,GAAG;QACjB,OAAO;YACL,IAAI,QAAQ,GAAG;QACjB;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,gCAAgC;IAChC,IAAI,CAAC,KAAK,UAAU,IAAI,CAAC,IAAI,QAAQ,CAAC,UAAU,CAAC,cAAc;QAC7D,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,IAAI,KAAK,UAAU,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,cAAc;QAC3D,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IACA,mEAAmE;IAEnE,4CAA4C;IAC5C,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,cAAc;QACxC,IAAI,QAAQ,GAAG;IACjB,OAAO;QACL,IAAI,QAAQ,GAAG;IACjB;IACA,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;AAE/B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}
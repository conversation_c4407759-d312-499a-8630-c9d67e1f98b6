import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

/**
 * Check if a JWT token is expired
 * @param token - JWT token to check
 * @returns true if token is expired, false otherwise
 */
const isTokenExpired = (token: string): boolean => {
  if (!token) return true;

  try {
    // Decode JWT payload (without verification - just for expiry check)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);

    // Check if token has expired
    return payload.exp < currentTime;
  } catch (error) {
    console.error('Error decoding token:', error);
    return true; // Treat invalid tokens as expired
  }
};

/**
 * Check if user is authorized for admin/staff routes
 * @param user - User object with email and roles
 * @returns true if user is authorized for admin routes, false otherwise
 */
const isAuthorizedForAdminRoutes = (user: any): boolean => {
  if (!user || !user.roles) return false;

  // Check if user has staff role
  const hasStaffRole = user.roles && !user.isCustomer;

  return hasStaffRole;
};

export function middleware(request: NextRequest) {
  const url = request.nextUrl.clone();

  // Get auth tokens from cookies
  const authToken = request.cookies.get('auth_token');
  const authUser = request.cookies.get('auth_user');

  // Parse user data if available
  let user = null;
  let token = null;

  // Try to get user auth
  if (authUser) {
    try {
      user = JSON.parse(authUser.value);
    } catch (error) {
      console.error('Failed to parse user data:', error);
    }
  }

  // Try to get token auth
  if (authToken) {
    try {
      token = JSON.parse(authToken.value);
    } catch (error) {
      console.error('Failed to parse token data:', error);
    }
  }


  // Always allow auth routes and public routes without redirection
  if (url.pathname.startsWith('/customer/auth/') ||
      url.pathname.startsWith('/auth/') ||
      url.pathname.startsWith('/documentations') ||
      url.pathname.startsWith('/public/')) {
    return NextResponse.next();
  }
  
  // Token Expired - redirection
  if (isTokenExpired(token ?? '') || !token || !user) {
    if (url.pathname.startsWith('/customer')) {
      url.pathname = '/customer/auth/login';
    } else {
      url.pathname = '/auth/login';
    }
    return NextResponse.redirect(url);
  }

  // Handle root path redirections
  if (!user.isCustomer && !url.pathname.startsWith('/customer')) {
    return NextResponse.next();
  } 

  if (user.isCustomer && url.pathname.startsWith('/customer')) {
    return NextResponse.next();
  } 
  // If already on auth page, allow through to prevent redirect loops
   
  // No valid url found - redirection to login
  if (url.pathname.startsWith('/customer')) {
    url.pathname = '/customer/auth/login';
  } else {
    url.pathname = '/auth/login';
  }
  return NextResponse.redirect(url);

}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images (public images)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|images).*)',
  ],
};

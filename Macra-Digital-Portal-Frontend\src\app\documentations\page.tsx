'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface DocumentationModule {
  id: string;
  title: string;
  description: string;
  icon: string;
  filename: string;
}

const documentationModules: DocumentationModule[] = [
  {
    id: 'authentication',
    title: 'Authentication',
    description: 'Registration, Login, and Password Management',
    icon: 'ri-user-line',
    filename: 'authentication.md'
  },
  {
    id: 'user-management',
    title: 'User Management',
    description: 'Managing user accounts and permissions (Admin only)',
    icon: 'ri-team-line',
    filename: 'user-management.md'
  },
  {
    id: 'license-applications',
    title: 'License Applications',
    description: 'How to apply for telecommunications licenses',
    icon: 'ri-file-text-line',
    filename: 'license-applications.md'
  },
  {
    id: 'document-management',
    title: 'Document Management',
    description: 'Uploading, viewing, and managing documents',
    icon: 'ri-folder-line',
    filename: 'document-management.md'
  },
  {
    id: 'application-tracking',
    title: 'Application Tracking',
    description: 'Monitoring your application status and progress',
    icon: 'ri-search-line',
    filename: 'application-tracking.md'
  },
  {
    id: 'payments',
    title: 'Payments & Billing',
    description: 'Processing payments and managing billing',
    icon: 'ri-money-dollar-circle-line',
    filename: 'payments.md'
  },
  {
    id: 'notifications',
    title: 'Notifications',
    description: 'Managing alerts and communication preferences',
    icon: 'ri-notification-line',
    filename: 'notifications.md'
  },
  {
    id: 'reports',
    title: 'Reports & Analytics',
    description: 'Generating reports and viewing analytics',
    icon: 'ri-bar-chart-line',
    filename: 'reports.md'
  }
];

export default function DocumentationsPage() {
  const [selectedModule, setSelectedModule] = useState<string>('authentication');
  const [markdownContent, setMarkdownContent] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState<boolean>(false);

  // Add custom styles for line clamping
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    `;
    document.head.appendChild(style);
    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []);

  useEffect(() => {
    loadMarkdownContent(selectedModule);
  }, [selectedModule]);

  // Handle escape key to close mobile sidebar
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isMobileSidebarOpen) {
        setIsMobileSidebarOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isMobileSidebarOpen]);

  // Prevent body scroll when mobile sidebar is open
  useEffect(() => {
    if (isMobileSidebarOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileSidebarOpen]);

  const loadMarkdownContent = async (moduleId: string) => {
    setLoading(true);
    setError('');
    
    try {
      const module = documentationModules.find(m => m.id === moduleId);
      if (!module) {
        throw new Error('Module not found');
      }

      const response = await fetch(`/docs/${module.filename}`);
      if (!response.ok) {
        throw new Error(`Failed to load documentation: ${response.statusText}`);
      }
      
      const content = await response.text();
      setMarkdownContent(content);
    } catch (err) {
      console.error('Error loading markdown:', err);
      setError('Failed to load documentation. Please try again later.');
      setMarkdownContent('');
    } finally {
      setLoading(false);
    }
  };

  const selectedModuleInfo = documentationModules.find(m => m.id === selectedModule);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-4 py-2 lg:py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 min-w-0 flex-1">
              <div className="flex-shrink-0">
                <i className="ri-book-open-line text-xl lg:text-2xl text-blue-600 dark:text-blue-400"></i>
              </div>
              <div className="min-w-0 flex-1">
                <h1 className="text-lg lg:text-2xl font-bold text-gray-900 dark:text-white truncate">
                  MACRA Digital Portal Documentation
                </h1>
                <p className="text-xs lg:text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Comprehensive guides and tutorials for using the portal
                </p>
              </div>
            </div>

            {/* Mobile Menu Toggle */}
            <button
              onClick={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
              className="lg:hidden flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              aria-label="Toggle documentation menu"
            >
              <i className={`ri-${isMobileSidebarOpen ? 'close' : 'menu'}-line text-lg`}></i>
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-full mx-auto px-2 lg:px-4 py-2 lg:py-4 overflow-hidden relative">
        {/* Mobile Overlay */}
        {isMobileSidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setIsMobileSidebarOpen(false)}
          />
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-2 lg:gap-4 min-h-0">
          {/* Sidebar - Module Menu */}
          <div className={`lg:col-span-1 order-2 lg:order-1 ${
            isMobileSidebarOpen
              ? 'fixed inset-y-0 left-0 z-50 w-80 bg-white dark:bg-gray-900 lg:relative lg:inset-auto lg:z-auto lg:w-auto lg:bg-transparent'
              : 'hidden lg:block'
          }`}>
            <Card className="lg:sticky h-full lg:h-auto">
              <CardHeader className="p-0">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
                    Documentation Modules
                  </CardTitle>
                  {/* Mobile Close Button */}
                  <button
                    onClick={() => setIsMobileSidebarOpen(false)}
                    className="lg:hidden flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                    aria-label="Close menu"
                  >
                    <i className="ri-close-line text-sm"></i>
                  </button>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="max-h-[calc(100vh-200px)] lg:max-h-[calc(100vh-200px)] overflow-y-auto overflow-x-hidden">
                  <div className="space-y-1 p-2">
                    {documentationModules.map((module) => (
                      <Button
                        key={module.id}
                        variant={selectedModule === module.id ? "default" : "ghost"}
                        className={`w-full justify-start text-left h-auto p-2 mb-2 rounded-lg transition-all duration-200 ${
                          selectedModule === module.id
                            ? 'bg-blue-600 text-white hover:bg-blue-700 shadow-md'
                            : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:shadow-sm'
                        }`}
                        onClick={() => {
                          setSelectedModule(module.id);
                          setIsMobileSidebarOpen(false);
                        }}
                      >
                        <div className="flex items-start space-x-2 w-full min-w-0">
                          <i className={`${module.icon} text-lg flex-shrink-0 mt-0.5`}></i>
                          <div className="text-left flex-1 min-w-0 overflow-hidden">
                            <div className="font-medium truncate">{module.title}</div>
                            <div className={`text-xs mt-1 leading-relaxed line-clamp-2 ${
                              selectedModule === module.id
                                ? 'text-blue-100'
                                : 'text-gray-500 dark:text-gray-400'
                            }`}>
                              {module.description}
                            </div>
                          </div>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3 order-1 lg:order-2 min-w-0 relative">
            {/* Mobile Module Selector Button */}
            <button
              onClick={() => setIsMobileSidebarOpen(true)}
              className="lg:hidden fixed bottom-6 right-6 z-30 flex items-center space-x-2 bg-blue-600 text-white px-4 py-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
              aria-label="Open documentation menu"
            >
              <i className={`${selectedModuleInfo?.icon} text-lg`}></i>
              <span className="text-sm font-medium max-w-[120px] truncate">
                {selectedModuleInfo?.title}
              </span>
              <i className="ri-arrow-up-s-line text-lg"></i>
            </button>

            <Card className="h-full">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between min-w-0">
                  <div className="flex items-center space-x-3 min-w-0 flex-1">
                    <i className={`${selectedModuleInfo?.icon} text-2xl text-blue-600 dark:text-blue-400 flex-shrink-0`}></i>
                    <div className="min-w-0 flex-1">
                      <CardTitle className="text-xl font-bold text-gray-900 dark:text-white truncate">
                        {selectedModuleInfo?.title}
                      </CardTitle>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                        {selectedModuleInfo?.description}
                      </p>
                    </div>
                  </div>

                  {/* Mobile Menu Button in Header */}
                  <button
                    onClick={() => setIsMobileSidebarOpen(true)}
                    className="lg:hidden flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors ml-3"
                    aria-label="Open documentation menu"
                  >
                    <i className="ri-menu-line text-lg"></i>
                  </button>
                </div>
              </CardHeader>
              <CardContent className="px-4 sm:px-4 pb-2">
                <div className="max-h-[600px] lg:max-h-[calc(100vh-200px)] overflow-y-auto overflow-x-hidden pr-2 lg:pr-2">
                  {loading && (
                    <div className="flex items-center justify-center py-12">
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                        <span className="text-gray-600 dark:text-gray-400">Loading documentation...</span>
                      </div>
                    </div>
                  )}

                  {error && (
                    <div className="flex items-center justify-center py-12">
                      <div className="text-center">
                        <i className="ri-error-warning-line text-4xl text-red-500 mb-4"></i>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                          Failed to Load Documentation
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
                        <Button 
                          onClick={() => loadMarkdownContent(selectedModule)}
                          variant="outline"
                        >
                          <i className="ri-refresh-line mr-2"></i>
                          Try Again
                        </Button>
                      </div>
                    </div>
                  )}

                  {!loading && !error && markdownContent && (
                    <div className="prose prose-gray dark:prose-invert max-w-none overflow-hidden">
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        components={{
                          h1: ({ children }) => (
                            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 pb-4 border-b-2 border-gray-200 dark:border-gray-700">
                              {children}
                            </h1>
                          ),
                          h2: ({ children }) => (
                            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mt-10 mb-6 pb-2 border-b border-gray-200 dark:border-gray-700">
                              {children}
                            </h2>
                          ),
                          h3: ({ children }) => (
                            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mt-8 mb-4">
                              {children}
                            </h3>
                          ),
                          h4: ({ children }) => (
                            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mt-6 mb-3">
                              {children}
                            </h4>
                          ),
                          p: ({ children }) => (
                            <p className="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed text-base break-words">
                              {children}
                            </p>
                          ),
                          ul: ({ children }) => (
                            <ul className="list-disc list-inside text-gray-700 dark:text-gray-300 mb-6 space-y-2 pl-4">
                              {children}
                            </ul>
                          ),
                          ol: ({ children }) => (
                            <ol className="list-decimal list-inside text-gray-700 dark:text-gray-300 mb-6 space-y-2 pl-4">
                              {children}
                            </ol>
                          ),
                          li: ({ children }) => (
                            <li className="text-gray-700 dark:text-gray-300 leading-relaxed">
                              {children}
                            </li>
                          ),
                          blockquote: ({ children }) => (
                            <blockquote className="border-l-4 border-blue-500 pl-6 py-4 bg-blue-50 dark:bg-blue-900/20 text-gray-700 dark:text-gray-300 mb-6 rounded-r-lg">
                              {children}
                            </blockquote>
                          ),
                          code: ({ children, className }) => {
                            const isInline = !className;
                            if (isInline) {
                              return (
                                <code className="bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-2 py-1 rounded text-sm font-mono break-words">
                                  {children}
                                </code>
                              );
                            }
                            return (
                              <code className="block bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 p-4 rounded-lg text-sm font-mono overflow-x-auto mb-6 whitespace-pre-wrap break-words">
                                {children}
                              </code>
                            );
                          },
                          strong: ({ children }) => (
                            <strong className="font-semibold text-gray-900 dark:text-white">
                              {children}
                            </strong>
                          ),
                          em: ({ children }) => (
                            <em className="italic text-gray-700 dark:text-gray-300">
                              {children}
                            </em>
                          ),
                        }}
                      >
                        {markdownContent}
                      </ReactMarkdown>
                    </div>
                  )}

                  {!loading && !error && !markdownContent && (
                    <div className="flex items-center justify-center py-12">
                      <div className="text-center">
                        <i className="ri-file-text-line text-4xl text-gray-400 mb-4"></i>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                          Documentation Coming Soon
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400">
                          This documentation module is currently being prepared.
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

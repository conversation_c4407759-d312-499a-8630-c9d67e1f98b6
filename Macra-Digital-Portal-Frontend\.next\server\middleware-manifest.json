{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_82ad035c._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_fb905aa2.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Y8JckVUlWFoRt22g0sJwhPNGOheToeZKXRmo3KpNRec=", "__NEXT_PREVIEW_MODE_ID": "dfa986248a050b1110bac90b96bc37f1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a091808cf1550bcbdb4020d2d029eeb73bd32cd1beba5fe3aa071b74e33a1f65", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8279c63770adfe150914ba556ecc6fc5a5138c789adbc6161a1f9afdf5dfd11c"}}}, "sortedMiddleware": ["/"], "functions": {}}
(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/forms/OTPInput.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
const OTPInput = (param)=>{
    let { length = 6, value, onChange, onComplete, disabled = false, autoFocus = false, placeholder = '', hasError = false, className = '', type = 'text', numericOnly = true } = param;
    _s();
    const [, setActiveIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const inputRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]);
    // Initialize refs array
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "OTPInput.useEffect": ()=>{
            inputRefs.current = inputRefs.current.slice(0, length);
        }
    }["OTPInput.useEffect"], [
        length
    ]);
    // Auto-focus first input if autoFocus is true
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "OTPInput.useEffect": ()=>{
            if (autoFocus && inputRefs.current[0]) {
                inputRefs.current[0].focus();
            }
        }
    }["OTPInput.useEffect"], [
        autoFocus
    ]);
    // Call onComplete when value reaches the required length
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "OTPInput.useEffect": ()=>{
            if (value.length === length && onComplete) {
                onComplete(value);
            }
        }
    }["OTPInput.useEffect"], [
        value,
        length,
        onComplete
    ]);
    // Convert value string to array for individual inputs
    const valueArray = value.split('').slice(0, length);
    while(valueArray.length < length){
        valueArray.push('');
    }
    const focusInput = (index)=>{
        if (inputRefs.current[index]) {
            var _inputRefs_current_index;
            (_inputRefs_current_index = inputRefs.current[index]) === null || _inputRefs_current_index === void 0 ? void 0 : _inputRefs_current_index.focus();
            setActiveIndex(index);
        }
    };
    const handleInputChange = (index, inputValue)=>{
        // Filter input based on numericOnly setting
        let filteredValue = inputValue;
        if (numericOnly) {
            filteredValue = inputValue.replace(/\D/g, '');
        }
        // Take only the last character if multiple characters are entered
        const newChar = filteredValue.slice(-1);
        // Update the value array
        const newValueArray = [
            ...valueArray
        ];
        newValueArray[index] = newChar;
        // Create new value string
        const newValue = newValueArray.join('');
        onChange(newValue);
        // Move to next input if a character was entered
        if (newChar && index < length - 1) {
            focusInput(index + 1);
        }
    };
    const handleKeyDown = (index, e)=>{
        switch(e.key){
            case 'Backspace':
                e.preventDefault();
                if (valueArray[index]) {
                    // Clear current input
                    const newValueArray = [
                        ...valueArray
                    ];
                    newValueArray[index] = '';
                    onChange(newValueArray.join(''));
                } else if (index > 0) {
                    // Move to previous input and clear it
                    const newValueArray = [
                        ...valueArray
                    ];
                    newValueArray[index - 1] = '';
                    onChange(newValueArray.join(''));
                    focusInput(index - 1);
                }
                break;
            case 'Delete':
                e.preventDefault();
                if (valueArray[index]) {
                    const newValueArray = [
                        ...valueArray
                    ];
                    newValueArray[index] = '';
                    onChange(newValueArray.join(''));
                }
                break;
            case 'ArrowLeft':
                e.preventDefault();
                if (index > 0) {
                    focusInput(index - 1);
                }
                break;
            case 'ArrowRight':
                e.preventDefault();
                if (index < length - 1) {
                    focusInput(index + 1);
                }
                break;
            case 'Home':
                e.preventDefault();
                focusInput(0);
                break;
            case 'End':
                e.preventDefault();
                focusInput(length - 1);
                break;
            default:
                // Allow numeric input if numericOnly is true
                if (numericOnly && !/^\d$/.test(e.key) && ![
                    'Tab'
                ].includes(e.key)) {
                    e.preventDefault();
                }
                break;
        }
    };
    const handlePaste = (e)=>{
        e.preventDefault();
        const pastedData = e.clipboardData.getData('text');
        // Filter pasted data based on numericOnly setting
        let filteredData = pastedData;
        if (numericOnly) {
            filteredData = pastedData.replace(/\D/g, '');
        }
        // Take only the required length
        const newValue = filteredData.slice(0, length);
        onChange(newValue);
        // Focus the next empty input or the last input
        const nextIndex = Math.min(newValue.length, length - 1);
        focusInput(nextIndex);
    };
    const handleFocus = (index)=>{
        setActiveIndex(index);
    };
    const baseInputClass = "\n    w-12 h-12 text-center text-lg font-mono border rounded-md\n    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\n    transition-colors duration-200\n    ".concat(hasError ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 dark:border-gray-600', "\n    ").concat(disabled ? 'bg-gray-100 dark:bg-gray-700 cursor-not-allowed opacity-50' : 'bg-white dark:bg-gray-800', "\n    text-gray-900 dark:text-white\n    placeholder-gray-400 dark:placeholder-gray-500\n  ").trim().replace(/\s+/g, ' ');
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex gap-2 justify-center ".concat(className),
        children: valueArray.map((digit, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                ref: (el)=>{
                    inputRefs.current[index] = el;
                },
                type: type,
                value: digit,
                onChange: (e)=>handleInputChange(index, e.target.value),
                onKeyDown: (e)=>handleKeyDown(index, e),
                onPaste: handlePaste,
                onFocus: ()=>handleFocus(index),
                disabled: disabled,
                placeholder: placeholder,
                className: baseInputClass,
                maxLength: 1,
                autoComplete: "one-time-code",
                inputMode: numericOnly ? 'numeric' : 'text',
                pattern: numericOnly ? '[0-9]*' : undefined
            }, index, false, {
                fileName: "[project]/src/components/forms/OTPInput.tsx",
                lineNumber: 181,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)))
    }, void 0, false, {
        fileName: "[project]/src/components/forms/OTPInput.tsx",
        lineNumber: 179,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(OTPInput, "OCHBKaGtX+bDHmymDNcFa6bRfCE=");
_c = OTPInput;
const __TURBOPACK__default__export__ = OTPInput;
var _c;
__turbopack_context__.k.register(_c, "OTPInput");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/forms/OTPVerification.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$forms$2f$OTPInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/forms/OTPInput.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
const OTPVerification = (param)=>{
    let { title = 'Enter Verification Code', description = 'Please enter the verification code sent to your email', value, onChange, onSubmit, error, loading = false, length = 6, submitText = 'Verify', loadingText = 'Verifying...', autoSubmit = false, showSubmitButton = true, className = '', autoFocus = true, numericOnly = true, children } = param;
    _s();
    const [isComplete, setIsComplete] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Check if OTP is complete
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "OTPVerification.useEffect": ()=>{
            const complete = value.length === length;
            setIsComplete(complete);
            // Auto-submit if enabled and OTP is complete
            if (autoSubmit && complete && !loading) {
                onSubmit(value);
            }
        }
    }["OTPVerification.useEffect"], [
        value,
        length,
        autoSubmit,
        loading,
        onSubmit
    ]);
    const handleSubmit = (e)=>{
        e.preventDefault();
        if (value.length === length && !loading) {
            onSubmit(value);
        }
    };
    const handleOTPComplete = (otpValue)=>{
        if (autoSubmit && !loading) {
            onSubmit(otpValue);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6 ".concat(className),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-medium text-gray-900 dark:text-white mb-2",
                        children: title
                    }, void 0, false, {
                        fileName: "[project]/src/components/forms/OTPVerification.tsx",
                        lineNumber: 56,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-600 dark:text-gray-400",
                        children: description
                    }, void 0, false, {
                        fileName: "[project]/src/components/forms/OTPVerification.tsx",
                        lineNumber: 59,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/forms/OTPVerification.tsx",
                lineNumber: 55,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            children,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                onSubmit: handleSubmit,
                className: "space-y-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col items-center space-y-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$forms$2f$OTPInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            length: length,
                            value: value,
                            onChange: onChange,
                            onComplete: handleOTPComplete,
                            disabled: loading,
                            autoFocus: autoFocus,
                            hasError: !!error,
                            numericOnly: numericOnly
                        }, void 0, false, {
                            fileName: "[project]/src/components/forms/OTPVerification.tsx",
                            lineNumber: 71,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg text-sm text-center max-w-md",
                            children: error
                        }, void 0, false, {
                            fileName: "[project]/src/components/forms/OTPVerification.tsx",
                            lineNumber: 84,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        showSubmitButton && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "submit",
                            disabled: !isComplete || loading,
                            className: "w-full max-w-xs flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200",
                            children: loading ? loadingText : submitText
                        }, void 0, false, {
                            fileName: "[project]/src/components/forms/OTPVerification.tsx",
                            lineNumber: 91,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/forms/OTPVerification.tsx",
                    lineNumber: 69,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/forms/OTPVerification.tsx",
                lineNumber: 68,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/forms/OTPVerification.tsx",
        lineNumber: 53,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(OTPVerification, "CSWYhrRR5s2SprqJSm+5pkQCw6w=");
_c = OTPVerification;
const __TURBOPACK__default__export__ = OTPVerification;
var _c;
__turbopack_context__.k.register(_c, "OTPVerification");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/customer/auth/verify-2fa/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>CustomerVerify2FAPage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/auth.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Loader.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$solid$2f$esm$2f$XCircleIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XCircleIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/solid/esm/XCircleIcon.js [app-client] (ecmascript) <export default as XCircleIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$forms$2f$OTPVerification$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/forms/OTPVerification.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
function CustomerVerify2FAPage() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { completeTwoFactorLogin, logout } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [success, setSuccess] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [verifyLoading, setVerifyLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [loadingMessage, setLoadingMessage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('Checking verification parameters...');
    const [unauthorizedAccess, setUnauthorizedAccess] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [formData, setFormData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        code: ''
    });
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const userId = searchParams.get('i') || '';
    const u = searchParams.get('unique') || '';
    const c = searchParams.get('c') || '';
    // Handle form data changes
    const handleFormChange = (field, value)=>{
        setFormData((prev)=>({
                ...prev,
                [field]: value
            }));
        // Clear errors when user starts typing
        if (error) setError('');
    };
    // Helper function to clear auth context with logging
    const clearAuthContext = (reason)=>{
        console.warn("Clearing auth context: ".concat(reason));
        // Clear any 2FA-related session storage
        if ("TURBOPACK compile-time truthy", 1) {
            sessionStorage.removeItem('remember_me');
            sessionStorage.removeItem('2fa_setup_user');
            sessionStorage.removeItem('2fa_setup_token');
            sessionStorage.removeItem('2fa_setup_redirect_back');
        }
        logout();
    };
    // Manual verification for user-entered OTP
    const handleManualVerify = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CustomerVerify2FAPage.useCallback[handleManualVerify]": async ()=>{
            if (!formData.code.trim() || verifyLoading) return;
            try {
                setVerifyLoading(true);
                setError('');
                const { access_token, user, message } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].verify2FA({
                    user_id: userId,
                    code: formData.code.trim(),
                    unique: u // Use the unique parameter from the verification link
                });
                if (access_token && user) {
                    setSuccess(message || 'Your account has been verified successfully!');
                    if (user.two_factor_enabled) {
                        // Retrieve rememberMe preference from sessionStorage
                        const rememberMe = sessionStorage.getItem('remember_me') === 'true';
                        await completeTwoFactorLogin(access_token, user, rememberMe);
                        // Clear the remember me preference from session storage
                        sessionStorage.removeItem('remember_me');
                        setTimeout({
                            "CustomerVerify2FAPage.useCallback[handleManualVerify]": ()=>{
                                router.push('/customer');
                            }
                        }["CustomerVerify2FAPage.useCallback[handleManualVerify]"], 2000);
                    } else {
                        // Don't set auth context - redirect to login for proper 2FA setup flow
                        console.log('2FA verification complete, redirecting to login for proper setup');
                        setTimeout({
                            "CustomerVerify2FAPage.useCallback[handleManualVerify]": ()=>{
                                router.push('/customer/auth/login');
                            }
                        }["CustomerVerify2FAPage.useCallback[handleManualVerify]"], 3000);
                    }
                } else {
                    throw new Error('Invalid response from server');
                }
            } catch (err) {
                var _err_response, _err_response1, _err_response2, _err_response3, _err_response_data, _err_response4, _err_response5;
                let errorMessage = 'Invalid verification code';
                if ((err === null || err === void 0 ? void 0 : (_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.status) === 400) {
                    errorMessage = 'Invalid verification code. Please check and try again.';
                } else if ((err === null || err === void 0 ? void 0 : (_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 401) {
                    errorMessage = 'Verification code has expired. Please request a new one.';
                    // Clear auth context for expired verification sessions
                    clearAuthContext('Verification session expired');
                } else if ((err === null || err === void 0 ? void 0 : (_err_response2 = err.response) === null || _err_response2 === void 0 ? void 0 : _err_response2.status) === 429) {
                    errorMessage = 'Too many attempts. Please wait before trying again.';
                } else if ((err === null || err === void 0 ? void 0 : (_err_response3 = err.response) === null || _err_response3 === void 0 ? void 0 : _err_response3.status) === 404) {
                    errorMessage = 'Verification session not found. Please try logging in again.';
                    // Clear auth context for missing verification sessions
                    clearAuthContext('Verification session not found');
                } else if (err === null || err === void 0 ? void 0 : (_err_response4 = err.response) === null || _err_response4 === void 0 ? void 0 : (_err_response_data = _err_response4.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.message) {
                    errorMessage = err.response.data.message;
                    // Handle specific backend messages
                    if (errorMessage.toLowerCase().includes('expired')) {
                        // Clear auth context for expired sessions
                        clearAuthContext('Verification session expired (backend message)');
                    } else if (errorMessage.toLowerCase().includes('invalid') && errorMessage.toLowerCase().includes('link')) {
                        // Clear auth context for invalid verification links
                        clearAuthContext('Invalid verification link detected');
                    }
                } else if (err === null || err === void 0 ? void 0 : err.message) {
                    errorMessage = err.message;
                }
                setError(errorMessage);
                // Clear the code if it's invalid to encourage re-entry
                if ((err === null || err === void 0 ? void 0 : (_err_response5 = err.response) === null || _err_response5 === void 0 ? void 0 : _err_response5.status) === 400) {
                    setFormData({
                        "CustomerVerify2FAPage.useCallback[handleManualVerify]": (prev)=>({
                                ...prev,
                                code: ''
                            })
                    }["CustomerVerify2FAPage.useCallback[handleManualVerify]"]);
                }
            } finally{
                setVerifyLoading(false);
            }
        }
    }["CustomerVerify2FAPage.useCallback[handleManualVerify]"], [
        formData.code,
        verifyLoading,
        userId,
        u,
        completeTwoFactorLogin,
        router,
        clearAuthContext
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CustomerVerify2FAPage.useEffect": ()=>{
            if (!userId || !u) {
                setLoading(false);
                const missingParams = [];
                if (!userId) missingParams.push('user ID');
                if (!u) missingParams.push('unique identifier');
                // Clear auth context variables for invalid/missing parameters
                clearAuthContext('Invalid verification link parameters detected');
                setUnauthorizedAccess(true);
                setError('Unauthorized access. This page can only be accessed through a valid 2FA verification link sent to your email.');
                setLoadingMessage('Please check your email for the verification link or login again.');
                setLoading(true);
                setTimeout({
                    "CustomerVerify2FAPage.useEffect": ()=>{
                        router.replace('/customer');
                    }
                }["CustomerVerify2FAPage.useEffect"], 7000);
                return;
            }
            // Set up manual input mode directly - all parameters are present
            setLoading(false);
        }
    }["CustomerVerify2FAPage.useEffect"], [
        router,
        userId,
        u,
        clearAuthContext
    ]);
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "sm:mx-auto sm:w-full sm:max-w-md",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    message: loadingMessage
                }, void 0, false, {
                    fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                    lineNumber: 171,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                lineNumber: 170,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
            lineNumber: 169,
            columnNumber: 7
        }, this);
    }
    const alreadyEnabled = success.toLowerCase().includes('enabled');
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "sm:mx-auto sm:w-full sm:max-w-md text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        src: "/images/macra-logo.png",
                        alt: "MACRA Logo",
                        width: 50,
                        height: 50,
                        className: "mx-auto h-16 w-auto"
                    }, void 0, false, {
                        fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                        lineNumber: 181,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "mt-6 text-3xl font-extrabold text-gray-900 dark:text-white",
                        children: success ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-green-900 dark:text-green-300",
                            children: "Verification Success!"
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                            lineNumber: 184,
                            columnNumber: 13
                        }, this) : error ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-red-800 dark:text-red-300",
                            children: "Error"
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                            lineNumber: 186,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-gray-600 dark:text-gray-300",
                            children: "Two-Factor Authentication"
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                            lineNumber: 188,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                        lineNumber: 182,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                lineNumber: 180,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-8 sm:mx-auto sm:w-full sm:max-w-md",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10",
                    children: [
                        error && !alreadyEnabled && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col flex-auto items-center justify-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center shadow-md",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$solid$2f$esm$2f$XCircleIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XCircleIcon$3e$__["XCircleIcon"], {
                                        className: "w-10 h-10 animate-pulse text-red-600 dark:text-red-300"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                                        lineNumber: 198,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                                    lineNumber: 197,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md text-center",
                                    children: error
                                }, void 0, false, {
                                    fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                                    lineNumber: 200,
                                    columnNumber: 15
                                }, this),
                                unauthorizedAccess && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-4 w-full",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>router.replace('/customer/auth/login'),
                                        className: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                                        children: "Go to Login"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                                        lineNumber: 205,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                                    lineNumber: 204,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                            lineNumber: 196,
                            columnNumber: 13
                        }, this),
                        !loading && !success && !unauthorizedAccess && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$forms$2f$OTPVerification$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            title: "Verify OTP",
                            description: "Please enter the 6-digit verification code sent to your email",
                            value: formData.code,
                            onChange: (value)=>handleFormChange('code', value),
                            onSubmit: ()=>handleManualVerify(),
                            error: error,
                            loading: verifyLoading,
                            submitText: "Verify Code",
                            loadingText: "Verifying...",
                            autoSubmit: false,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    type: "button",
                                    onClick: ()=>router.replace('/customer/auth/login'),
                                    disabled: verifyLoading,
                                    className: "w-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",
                                    children: "Back to Login"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                                    lineNumber: 230,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                                lineNumber: 229,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                            lineNumber: 217,
                            columnNumber: 13
                        }, this),
                        (success || alreadyEnabled) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col flex-auto items-center justify-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        className: "w-8 h-8 text-green-600 dark:text-green-300",
                                        fill: "none",
                                        stroke: "currentColor",
                                        strokeWidth: "3",
                                        viewBox: "0 0 24 24",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            d: "M5 13l4 4L19 7"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                                            lineNumber: 252,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                                        lineNumber: 245,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                                    lineNumber: 244,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center text-gray-600 dark:text-gray-400",
                                    children: success
                                }, void 0, false, {
                                    fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                                    lineNumber: 255,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                            lineNumber: 243,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                    lineNumber: 194,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
                lineNumber: 193,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/customer/auth/verify-2fa/page.tsx",
        lineNumber: 179,
        columnNumber: 5
    }, this);
}
_s(CustomerVerify2FAPage, "2VcHnP0ppLka0VyiYWVAWz/gcG0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"]
    ];
});
_c = CustomerVerify2FAPage;
var _c;
__turbopack_context__.k.register(_c, "CustomerVerify2FAPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/@heroicons/react/24/solid/esm/XCircleIcon.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function XCircleIcon(param, svgRef) {
    let { title, titleId, ...props } = param;
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"]("svg", Object.assign({
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 24 24",
        fill: "currentColor",
        "aria-hidden": "true",
        "data-slot": "icon",
        ref: svgRef,
        "aria-labelledby": titleId
    }, props), title ? /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"]("title", {
        id: titleId
    }, title) : null, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"]("path", {
        fillRule: "evenodd",
        d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",
        clipRule: "evenodd"
    }));
}
const ForwardRef = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"](XCircleIcon);
const __TURBOPACK__default__export__ = ForwardRef;
}),
"[project]/node_modules/@heroicons/react/24/solid/esm/XCircleIcon.js [app-client] (ecmascript) <export default as XCircleIcon>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "XCircleIcon": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$solid$2f$esm$2f$XCircleIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$solid$2f$esm$2f$XCircleIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/solid/esm/XCircleIcon.js [app-client] (ecmascript)");
}),
}]);

//# sourceMappingURL=_0a8f5922._.js.map
'use client';

import { useCallback, useEffect, useState } from 'react';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { authService } from '@/services/auth.service';
import {
  XCircleIcon,
} from '@heroicons/react/24/solid';

import Loader from '@/components/Loader';
import { useAuth } from '@/contexts/AuthContext';
import OTPVerification from '@/components/forms/OTPVerification';

export default function StaffVerifyTwoFactorPage() {
  const router = useRouter();
  const { completeTwoFactorLogin, logout } = useAuth();
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(true);
  const [verifyLoading, setVerifyLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('Checking verification parameters...');
  const [unauthorizedAccess, setUnauthorizedAccess] = useState(false);
  const [formData, setFormData] = useState({
    code: ''
  });
  const searchParams = useSearchParams();
  const userId = searchParams.get('i') || '';
  const u = searchParams.get('unique') || '';
  const c = searchParams.get('c') || '';



  // Handle form data changes
  const handleFormChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear errors when user starts typing
    if (error) setError('');
  };



  // Helper function to clear auth context with logging
  const clearAuthContext = (reason: string) => {
    console.warn(`Clearing auth context: ${reason}`);

    // Clear any 2FA-related session storage
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem('remember_me');
      sessionStorage.removeItem('2fa_setup_user');
      sessionStorage.removeItem('2fa_setup_token');
      sessionStorage.removeItem('2fa_setup_redirect_back');
    }

    logout();
  };


  // Manual verification for user-entered OTP
  const handleManualVerify = useCallback(async () => {
    if (!formData.code.trim() || verifyLoading) return;

    try {
      setVerifyLoading(true);
      setError('');

      const { access_token, user, message } = await authService.verify2FA({
        user_id: userId,
        code: formData.code.trim(),
        unique: u // Use the unique parameter from the verification link
      });

      if (access_token && user) {
        setSuccess(message || 'Your account has been verified successfully!');

        if (user.two_factor_enabled) {
          // Retrieve rememberMe preference from sessionStorage
          const rememberMe = sessionStorage.getItem('remember_me') === 'true';
          await completeTwoFactorLogin(access_token, user, rememberMe);
          // Clear the remember me preference from session storage
          sessionStorage.removeItem('remember_me');
          setTimeout(() => {
            router.push('/dashboard');
          }, 2000);
        } else {
          // Don't set auth context - redirect to login for proper 2FA setup flow
          console.log('2FA verification complete, redirecting to login for proper setup');
          setTimeout(() => {
            router.push('/auth/login');
          }, 3000);
        }
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (err: any) {
      let errorMessage = 'Invalid verification code';

      if (err?.response?.status === 400) {
        errorMessage = 'Invalid verification code. Please check and try again.';
      } else if (err?.response?.status === 401) {
        errorMessage = 'Verification code has expired. Please request a new one.';
        // Clear auth context for expired verification sessions
        clearAuthContext('Verification session expired');
      } else if (err?.response?.status === 429) {
        errorMessage = 'Too many attempts. Please wait before trying again.';
      } else if (err?.response?.status === 404) {
        errorMessage = 'Verification session not found. Please try logging in again.';
        // Clear auth context for missing verification sessions
        clearAuthContext('Verification session not found');
      } else if (err?.response?.data?.message) {
        errorMessage = err.response.data.message;
        // Handle specific backend messages
        if (errorMessage.toLowerCase().includes('expired')) {
          // Clear auth context for expired sessions
          clearAuthContext('Verification session expired (backend message)');
        } else if (errorMessage.toLowerCase().includes('invalid') &&
                   errorMessage.toLowerCase().includes('link')) {
          // Clear auth context for invalid verification links
          clearAuthContext('Invalid verification link detected');
        }
      } else if (err?.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);

      // Clear the code if it's invalid to encourage re-entry
      if (err?.response?.status === 400) {
        setFormData(prev => ({ ...prev, code: '' }));
      }
    } finally {
      setVerifyLoading(false);
    }
  }, [formData.code, verifyLoading, userId, u, completeTwoFactorLogin, router, clearAuthContext]);



  useEffect(() => {

    // Check URL parameters for comprehensive validation
    if (!userId || !u || !c) {
      setLoading(false);
      const missingParams = [];
      if (!userId) missingParams.push('user ID');
      if (!u) missingParams.push('unique identifier');
      if (!c) missingParams.push('verification code');

      // Clear auth context variables for invalid/missing parameters
      clearAuthContext('Invalid verification link parameters detected');

      setUnauthorizedAccess(true);
      setError('Unauthorized access. This page can only be accessed through a valid 2FA verification link sent to your email.');
      setLoadingMessage('Please check your email for the verification link or login again.');
      setLoading(true);

      setTimeout(() => {
        router.replace('/auth/login');
      }, 7000);

      return;
    }

    // Set up manual input mode directly - all parameters are present
    setLoading(false);
  }, [router, userId, u, c, clearAuthContext]);



  if (loading) {
    return (
      <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <Loader message={loadingMessage} />
        </div>
      </div>
    );
  }



  return (
    <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
      <div className="sm:mx-auto sm:w-full sm:max-w-md text-center">
        <Image src="/images/macra-logo.png" alt="MACRA Logo" width={50} height={50} className="mx-auto h-16 w-auto" />
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
          {success ? (
            <span className="text-green-800 dark:text-green-300">Verification Success!</span>
          ) : error ? (
            <span className="text-red-800 dark:text-red-300">Error</span>
          ) : (
            <span className="text-gray-600 dark:text-gray-300">Two-Factor Authentication</span>
          )}
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10">
          {error && unauthorizedAccess && (
            <div className="flex flex-col flex-auto items-center justify-center">
              <div className="w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center shadow-md">
                <XCircleIcon className="w-10 h-10 animate-pulse text-red-600 dark:text-red-300" />
              </div>
              <div className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md text-center">
                {error}
              </div>
              <div className="mt-4 w-full">
                <button
                  onClick={() => router.replace('/auth/login')}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  Go to Login
                </button>
              </div>
            </div>
          )}

          {success && (
            <div className="flex flex-col flex-auto items-center justify-center">
              <div className="w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md">
                <svg
                  className="w-8 h-8 text-green-600 dark:text-green-300"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="3"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div className="text-center text-gray-600 dark:text-gray-400">
                {success} <br />
              </div>
            </div>
          )}

          {!loading && !success && !unauthorizedAccess && (
            <OTPVerification
              title="Verify OTP"
              description="Please enter the 6-digit verification code from your authenticator app or the code sent to your email"
              value={formData.code}
              onChange={(value: string) => handleFormChange('code', value)}
              onSubmit={() => handleManualVerify()}
              error={error && !unauthorizedAccess ? error : undefined}
              loading={verifyLoading}
              submitText="Verify Code"
              loadingText="Verifying..."
              autoSubmit={false}
            >
              <div className="mt-4">
                <button
                  type="button"
                  onClick={() => router.replace('/auth/login')}
                  disabled={verifyLoading}
                  className="w-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Back to Login
                </button>
              </div>
            </OTPVerification>
          )}

        </div>
      </div>
    </div>
  );
}

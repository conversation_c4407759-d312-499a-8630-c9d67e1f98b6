{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/OTPInput.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { OTPInputProps } from '@/types/user';\r\nimport React, { useState, useRef, useEffect, KeyboardEvent, ClipboardEvent } from 'react';\r\n\r\n\r\nconst OTPInput: React.FC<OTPInputProps> = ({\r\n  length = 6,\r\n  value,\r\n  onChange,\r\n  onComplete,\r\n  disabled = false,\r\n  autoFocus = false,\r\n  placeholder = '',\r\n  hasError = false,\r\n  className = '',\r\n  type = 'text',\r\n  numericOnly = true,\r\n}) => {\r\n  const [, setActiveIndex] = useState(0);\r\n  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);\r\n\r\n  // Initialize refs array\r\n  useEffect(() => {\r\n    inputRefs.current = inputRefs.current.slice(0, length);\r\n  }, [length]);\r\n\r\n  // Auto-focus first input if autoFocus is true\r\n  useEffect(() => {\r\n    if (autoFocus && inputRefs.current[0]) {\r\n      inputRefs.current[0].focus();\r\n    }\r\n  }, [autoFocus]);\r\n\r\n  // Call onComplete when value reaches the required length\r\n  useEffect(() => {\r\n    if (value.length === length && onComplete) {\r\n      onComplete(value);\r\n    }\r\n  }, [value, length, onComplete]);\r\n\r\n  // Convert value string to array for individual inputs\r\n  const valueArray = value.split('').slice(0, length);\r\n  while (valueArray.length < length) {\r\n    valueArray.push('');\r\n  }\r\n\r\n  const focusInput = (index: number) => {\r\n    if (inputRefs.current[index]) {\r\n      inputRefs.current[index]?.focus();\r\n      setActiveIndex(index);\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (index: number, inputValue: string) => {\r\n    // Filter input based on numericOnly setting\r\n    let filteredValue = inputValue;\r\n    if (numericOnly) {\r\n      filteredValue = inputValue.replace(/\\D/g, '');\r\n    }\r\n\r\n    // Take only the last character if multiple characters are entered\r\n    const newChar = filteredValue.slice(-1);\r\n\r\n    // Update the value array\r\n    const newValueArray = [...valueArray];\r\n    newValueArray[index] = newChar;\r\n\r\n    // Create new value string\r\n    const newValue = newValueArray.join('');\r\n    onChange(newValue);\r\n\r\n    // Move to next input if a character was entered\r\n    if (newChar && index < length - 1) {\r\n      focusInput(index + 1);\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {\r\n    switch (e.key) {\r\n      case 'Backspace':\r\n        e.preventDefault();\r\n        if (valueArray[index]) {\r\n          // Clear current input\r\n          const newValueArray = [...valueArray];\r\n          newValueArray[index] = '';\r\n          onChange(newValueArray.join(''));\r\n        } else if (index > 0) {\r\n          // Move to previous input and clear it\r\n          const newValueArray = [...valueArray];\r\n          newValueArray[index - 1] = '';\r\n          onChange(newValueArray.join(''));\r\n          focusInput(index - 1);\r\n        }\r\n        break;\r\n\r\n      case 'Delete':\r\n        e.preventDefault();\r\n        if (valueArray[index]) {\r\n          const newValueArray = [...valueArray];\r\n          newValueArray[index] = '';\r\n          onChange(newValueArray.join(''));\r\n        }\r\n        break;\r\n\r\n      case 'ArrowLeft':\r\n        e.preventDefault();\r\n        if (index > 0) {\r\n          focusInput(index - 1);\r\n        }\r\n        break;\r\n\r\n      case 'ArrowRight':\r\n        e.preventDefault();\r\n        if (index < length - 1) {\r\n          focusInput(index + 1);\r\n        }\r\n        break;\r\n\r\n      case 'Home':\r\n        e.preventDefault();\r\n        focusInput(0);\r\n        break;\r\n\r\n      case 'End':\r\n        e.preventDefault();\r\n        focusInput(length - 1);\r\n        break;\r\n\r\n      default:\r\n        // Allow numeric input if numericOnly is true\r\n        if (numericOnly && !/^\\d$/.test(e.key) && !['Tab'].includes(e.key)) {\r\n          e.preventDefault();\r\n        }\r\n        break;\r\n    }\r\n  };\r\n\r\n  const handlePaste = (e: ClipboardEvent<HTMLInputElement>) => {\r\n    e.preventDefault();\r\n    const pastedData = e.clipboardData.getData('text');\r\n    \r\n    // Filter pasted data based on numericOnly setting\r\n    let filteredData = pastedData;\r\n    if (numericOnly) {\r\n      filteredData = pastedData.replace(/\\D/g, '');\r\n    }\r\n\r\n    // Take only the required length\r\n    const newValue = filteredData.slice(0, length);\r\n    onChange(newValue);\r\n\r\n    // Focus the next empty input or the last input\r\n    const nextIndex = Math.min(newValue.length, length - 1);\r\n    focusInput(nextIndex);\r\n  };\r\n\r\n  const handleFocus = (index: number) => {\r\n    setActiveIndex(index);\r\n  };\r\n\r\n  const baseInputClass = `\r\n    w-12 h-12 text-center text-lg font-mono border rounded-md\r\n    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\r\n    transition-colors duration-200\r\n    ${hasError \r\n      ? 'border-red-500 focus:ring-red-500 focus:border-red-500' \r\n      : 'border-gray-300 dark:border-gray-600'\r\n    }\r\n    ${disabled \r\n      ? 'bg-gray-100 dark:bg-gray-700 cursor-not-allowed opacity-50' \r\n      : 'bg-white dark:bg-gray-800'\r\n    }\r\n    text-gray-900 dark:text-white\r\n    placeholder-gray-400 dark:placeholder-gray-500\r\n  `.trim().replace(/\\s+/g, ' ');\r\n\r\n  return (\r\n    <div className={`flex gap-2 justify-center ${className}`}>\r\n      {valueArray.map((digit, index) => (\r\n        <input\r\n          key={index}\r\n          ref={(el) => { inputRefs.current[index] = el; }}\r\n          type={type}\r\n          value={digit}\r\n          onChange={(e) => handleInputChange(index, e.target.value)}\r\n          onKeyDown={(e) => handleKeyDown(index, e)}\r\n          onPaste={handlePaste}\r\n          onFocus={() => handleFocus(index)}\r\n          disabled={disabled}\r\n          placeholder={placeholder}\r\n          className={baseInputClass}\r\n          maxLength={1}\r\n          autoComplete=\"one-time-code\"\r\n          inputMode={numericOnly ? 'numeric' : 'text'}\r\n          pattern={numericOnly ? '[0-9]*' : undefined}\r\n        />\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default OTPInput;\r\n"], "names": [], "mappings": ";;;;AAGA;;;AAHA;;AAMA,MAAM,WAAoC;QAAC,EACzC,SAAS,CAAC,EACV,KAAK,EACL,QAAQ,EACR,UAAU,EACV,WAAW,KAAK,EAChB,YAAY,KAAK,EACjB,cAAc,EAAE,EAChB,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,OAAO,MAAM,EACb,cAAc,IAAI,EACnB;;IACC,MAAM,GAAG,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACpC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA+B,EAAE;IAExD,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,UAAU,OAAO,GAAG,UAAU,OAAO,CAAC,KAAK,CAAC,GAAG;QACjD;6BAAG;QAAC;KAAO;IAEX,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,aAAa,UAAU,OAAO,CAAC,EAAE,EAAE;gBACrC,UAAU,OAAO,CAAC,EAAE,CAAC,KAAK;YAC5B;QACF;6BAAG;QAAC;KAAU;IAEd,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,MAAM,MAAM,KAAK,UAAU,YAAY;gBACzC,WAAW;YACb;QACF;6BAAG;QAAC;QAAO;QAAQ;KAAW;IAE9B,sDAAsD;IACtD,MAAM,aAAa,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG;IAC5C,MAAO,WAAW,MAAM,GAAG,OAAQ;QACjC,WAAW,IAAI,CAAC;IAClB;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU,OAAO,CAAC,MAAM,EAAE;gBAC5B;aAAA,2BAAA,UAAU,OAAO,CAAC,MAAM,cAAxB,+CAAA,yBAA0B,KAAK;YAC/B,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,4CAA4C;QAC5C,IAAI,gBAAgB;QACpB,IAAI,aAAa;YACf,gBAAgB,WAAW,OAAO,CAAC,OAAO;QAC5C;QAEA,kEAAkE;QAClE,MAAM,UAAU,cAAc,KAAK,CAAC,CAAC;QAErC,yBAAyB;QACzB,MAAM,gBAAgB;eAAI;SAAW;QACrC,aAAa,CAAC,MAAM,GAAG;QAEvB,0BAA0B;QAC1B,MAAM,WAAW,cAAc,IAAI,CAAC;QACpC,SAAS;QAET,gDAAgD;QAChD,IAAI,WAAW,QAAQ,SAAS,GAAG;YACjC,WAAW,QAAQ;QACrB;IACF;IAEA,MAAM,gBAAgB,CAAC,OAAe;QACpC,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,UAAU,CAAC,MAAM,EAAE;oBACrB,sBAAsB;oBACtB,MAAM,gBAAgB;2BAAI;qBAAW;oBACrC,aAAa,CAAC,MAAM,GAAG;oBACvB,SAAS,cAAc,IAAI,CAAC;gBAC9B,OAAO,IAAI,QAAQ,GAAG;oBACpB,sCAAsC;oBACtC,MAAM,gBAAgB;2BAAI;qBAAW;oBACrC,aAAa,CAAC,QAAQ,EAAE,GAAG;oBAC3B,SAAS,cAAc,IAAI,CAAC;oBAC5B,WAAW,QAAQ;gBACrB;gBACA;YAEF,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,UAAU,CAAC,MAAM,EAAE;oBACrB,MAAM,gBAAgB;2BAAI;qBAAW;oBACrC,aAAa,CAAC,MAAM,GAAG;oBACvB,SAAS,cAAc,IAAI,CAAC;gBAC9B;gBACA;YAEF,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,QAAQ,GAAG;oBACb,WAAW,QAAQ;gBACrB;gBACA;YAEF,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,QAAQ,SAAS,GAAG;oBACtB,WAAW,QAAQ;gBACrB;gBACA;YAEF,KAAK;gBACH,EAAE,cAAc;gBAChB,WAAW;gBACX;YAEF,KAAK;gBACH,EAAE,cAAc;gBAChB,WAAW,SAAS;gBACpB;YAEF;gBACE,6CAA6C;gBAC7C,IAAI,eAAe,CAAC,OAAO,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC;oBAAC;iBAAM,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG;oBAClE,EAAE,cAAc;gBAClB;gBACA;QACJ;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,EAAE,cAAc;QAChB,MAAM,aAAa,EAAE,aAAa,CAAC,OAAO,CAAC;QAE3C,kDAAkD;QAClD,IAAI,eAAe;QACnB,IAAI,aAAa;YACf,eAAe,WAAW,OAAO,CAAC,OAAO;QAC3C;QAEA,gCAAgC;QAChC,MAAM,WAAW,aAAa,KAAK,CAAC,GAAG;QACvC,SAAS;QAET,+CAA+C;QAC/C,MAAM,YAAY,KAAK,GAAG,CAAC,SAAS,MAAM,EAAE,SAAS;QACrD,WAAW;IACb;IAEA,MAAM,cAAc,CAAC;QACnB,eAAe;IACjB;IAEA,MAAM,iBAAiB,AAAC,2LAQpB,OAJA,WACE,2DACA,wCACH,UAIA,OAHC,WACE,+DACA,6BACH,+FAGD,IAAI,GAAG,OAAO,CAAC,QAAQ;IAEzB,qBACE,6LAAC;QAAI,WAAW,AAAC,6BAAsC,OAAV;kBAC1C,WAAW,GAAG,CAAC,CAAC,OAAO,sBACtB,6LAAC;gBAEC,KAAK,CAAC;oBAAS,UAAU,OAAO,CAAC,MAAM,GAAG;gBAAI;gBAC9C,MAAM;gBACN,OAAO;gBACP,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;gBACxD,WAAW,CAAC,IAAM,cAAc,OAAO;gBACvC,SAAS;gBACT,SAAS,IAAM,YAAY;gBAC3B,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,WAAW;gBACX,cAAa;gBACb,WAAW,cAAc,YAAY;gBACrC,SAAS,cAAc,WAAW;eAd7B;;;;;;;;;;AAmBf;GAlMM;KAAA;uCAoMS", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/OTPVerification.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport OTPInput from './OTPInput';\r\nimport { OTPVerificationProps } from '@/types/user';\r\n\r\n\r\nconst OTPVerification: React.FC<OTPVerificationProps> = ({\r\n  title = 'Enter Verification Code',\r\n  description = 'Please enter the verification code sent to your email',\r\n  value,\r\n  onChange,\r\n  onSubmit,\r\n  error,\r\n  loading = false,\r\n  length = 6,\r\n  submitText = 'Verify',\r\n  loadingText = 'Verifying...',\r\n  autoSubmit = false,\r\n  showSubmitButton = true,\r\n  className = '',\r\n  autoFocus = true,\r\n  numericOnly = true,\r\n  children,\r\n}) => {\r\n  const [isComplete, setIsComplete] = useState(false);\r\n\r\n  // Check if OTP is complete\r\n  useEffect(() => {\r\n    const complete = value.length === length;\r\n    setIsComplete(complete);\r\n\r\n    // Auto-submit if enabled and OTP is complete\r\n    if (autoSubmit && complete && !loading) {\r\n      onSubmit(value);\r\n    }\r\n  }, [value, length, autoSubmit, loading, onSubmit]);\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (value.length === length && !loading) {\r\n      onSubmit(value);\r\n    }\r\n  };\r\n\r\n  const handleOTPComplete = (otpValue: string) => {\r\n    if (autoSubmit && !loading) {\r\n      onSubmit(otpValue);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`space-y-6 ${className}`}>\r\n      {/* Header */}\r\n      <div className=\"text-center\">\r\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\r\n          {title}\r\n        </h3>\r\n        <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n          {description}\r\n        </p>\r\n      </div>\r\n\r\n      {/* Additional content */}\r\n      {children}\r\n\r\n      {/* OTP Form */}\r\n      <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n        <div className=\"flex flex-col items-center space-y-4\">\r\n          {/* OTP Input */}\r\n          <OTPInput\r\n            length={length}\r\n            value={value}\r\n            onChange={onChange}\r\n            onComplete={handleOTPComplete}\r\n            disabled={loading}\r\n            autoFocus={autoFocus}\r\n            hasError={!!error}\r\n            numericOnly={numericOnly}\r\n          />\r\n\r\n          {/* Error Message */}\r\n          {error && (\r\n            <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg text-sm text-center max-w-md\">\r\n              {error}\r\n            </div>\r\n          )}\r\n\r\n          {/* Submit Button */}\r\n          {showSubmitButton && (\r\n            <button\r\n              type=\"submit\"\r\n              disabled={!isComplete || loading}\r\n              className=\"w-full max-w-xs flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\"\r\n            >\r\n              {loading ? loadingText : submitText}\r\n            </button>\r\n          )}\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default OTPVerification;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAOA,MAAM,kBAAkD;QAAC,EACvD,QAAQ,yBAAyB,EACjC,cAAc,uDAAuD,EACrE,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,UAAU,KAAK,EACf,SAAS,CAAC,EACV,aAAa,QAAQ,EACrB,cAAc,cAAc,EAC5B,aAAa,KAAK,EAClB,mBAAmB,IAAI,EACvB,YAAY,EAAE,EACd,YAAY,IAAI,EAChB,cAAc,IAAI,EAClB,QAAQ,EACT;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,cAAc;YAEd,6CAA6C;YAC7C,IAAI,cAAc,YAAY,CAAC,SAAS;gBACtC,SAAS;YACX;QACF;oCAAG;QAAC;QAAO;QAAQ;QAAY;QAAS;KAAS;IAEjD,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,MAAM,MAAM,KAAK,UAAU,CAAC,SAAS;YACvC,SAAS;QACX;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,cAAc,CAAC,SAAS;YAC1B,SAAS;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,aAAsB,OAAV;;0BAE3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX;;;;;;kCAEH,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;YAKJ;0BAGD,6LAAC;gBAAK,UAAU;gBAAc,WAAU;0BACtC,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,0IAAA,CAAA,UAAQ;4BACP,QAAQ;4BACR,OAAO;4BACP,UAAU;4BACV,YAAY;4BACZ,UAAU;4BACV,WAAW;4BACX,UAAU,CAAC,CAAC;4BACZ,aAAa;;;;;;wBAId,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;wBAKJ,kCACC,6LAAC;4BACC,MAAK;4BACL,UAAU,CAAC,cAAc;4BACzB,WAAU;sCAET,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;AAOvC;GA/FM;KAAA;uCAiGS", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/auth/verify-2fa/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useCallback, useEffect, useState } from 'react';\r\nimport Image from 'next/image';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport { authService } from '@/services/auth.service';\r\n\r\nimport Loader from '@/components/Loader';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport {\r\n  XCircleIcon,\r\n} from '@heroicons/react/24/solid';\r\nimport OTPVerification from '@/components/forms/OTPVerification';\r\n\r\nexport default function CustomerVerify2FAPage() {\r\n  const router = useRouter();\r\n  const { completeTwoFactorLogin, logout } = useAuth();\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [loading, setLoading] = useState(true);\r\n  const [verifyLoading, setVerifyLoading] = useState(false);\r\n  const [resendLoading, setResendLoading] = useState(false);\r\n  const [loadingMessage, setLoadingMessage] = useState('Checking verification parameters...');\r\n  const [unauthorizedAccess, setUnauthorizedAccess] = useState(false);\r\n  const [formData, setFormData] = useState({\r\n    code: ''\r\n  });\r\n\r\n  const searchParams = useSearchParams();\r\n  const [userId, setUserId] = useState(searchParams.get('i') || '');\r\n  const u = searchParams.get('unique') || '';\r\n  const c = searchParams.get('c') || '';\r\n\r\n  // Handle form data changes\r\n  const handleFormChange = (field: string, value: string) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [field]: value\r\n    }));\r\n    // Clear errors when user starts typing\r\n    if (error) setError('');\r\n  };\r\n\r\n\r\n\r\n  // Helper function to clear auth context with logging\r\n  const clearAuthContext = (reason: string) => {\r\n    console.warn(`Clearing auth context: ${reason}`);\r\n\r\n    // Clear any 2FA-related session storage\r\n    if (typeof window !== 'undefined') {\r\n      sessionStorage.removeItem('remember_me');\r\n      sessionStorage.removeItem('2fa_setup_user');\r\n      sessionStorage.removeItem('2fa_setup_token');\r\n      sessionStorage.removeItem('2fa_setup_redirect_back');\r\n    }\r\n\r\n    logout();\r\n  };\r\n\r\n\r\n\r\n  // Manual verification for user-entered OTP\r\n  const handleManualVerify = useCallback(async () => {\r\n    if (!formData.code.trim() || verifyLoading) return;\r\n\r\n    try {\r\n      setVerifyLoading(true);\r\n      setError('');\r\n\r\n      const { access_token, user, message } = await authService.verify2FA({\r\n        user_id: userId,\r\n        code: formData.code.trim(),\r\n        unique: u || '' // Use the unique parameter from the verification link, or empty for login OTP\r\n      });\r\n\r\n      if (access_token && user) {\r\n        setSuccess(message || 'Your account has been verified successfully!');\r\n\r\n        if (user.two_factor_enabled) {\r\n          // Retrieve rememberMe preference from sessionStorage\r\n          const rememberMe = sessionStorage.getItem('remember_me') === 'true';\r\n          await completeTwoFactorLogin(access_token, user, rememberMe);\r\n          // Clear session storage\r\n          sessionStorage.removeItem('remember_me');\r\n          sessionStorage.removeItem('2fa_login_user');\r\n          setTimeout(() => {\r\n            router.push('/customer');\r\n          }, 2000);\r\n        } else {\r\n          // Don't set auth context - redirect to login for proper 2FA setup flow\r\n          console.log('2FA verification complete, redirecting to login for proper setup');\r\n          setTimeout(() => {\r\n            router.push('/customer/auth/login');\r\n          }, 3000);\r\n        }\r\n      } else {\r\n        throw new Error('Invalid response from server');\r\n      }\r\n    } catch (err: any) {\r\n      let errorMessage = 'Invalid verification code';\r\n\r\n      if (err?.response?.status === 400) {\r\n        errorMessage = 'Invalid verification code. Please check and try again.';\r\n      } else if (err?.response?.status === 401) {\r\n        errorMessage = 'Verification code has expired. Please request a new one.';\r\n        // Clear auth context for expired verification sessions\r\n        clearAuthContext('Verification session expired');\r\n      } else if (err?.response?.status === 429) {\r\n        errorMessage = 'Too many attempts. Please wait before trying again.';\r\n      } else if (err?.response?.status === 404) {\r\n        errorMessage = 'Verification session not found. Please try logging in again.';\r\n        // Clear auth context for missing verification sessions\r\n        clearAuthContext('Verification session not found');\r\n      } else if (err?.response?.data?.message) {\r\n        errorMessage = err.response.data.message;\r\n        // Handle specific backend messages\r\n        if (errorMessage.toLowerCase().includes('expired')) {\r\n          // Clear auth context for expired sessions\r\n          clearAuthContext('Verification session expired (backend message)');\r\n        } else if (errorMessage.toLowerCase().includes('invalid') &&\r\n                   errorMessage.toLowerCase().includes('link')) {\r\n          // Clear auth context for invalid verification links\r\n          clearAuthContext('Invalid verification link detected');\r\n        }\r\n      } else if (err?.message) {\r\n        errorMessage = err.message;\r\n      }\r\n\r\n      setError(errorMessage);\r\n\r\n      // Clear the code if it's invalid to encourage re-entry\r\n      if (err?.response?.status === 400) {\r\n        setFormData(prev => ({ ...prev, code: '' }));\r\n      }\r\n    } finally {\r\n      setVerifyLoading(false);\r\n    }\r\n  }, [formData.code, verifyLoading, userId, u, completeTwoFactorLogin, router, clearAuthContext]);\r\n\r\n  const handleResendCode = async () => {\r\n    if (!userId) return;\r\n\r\n    setResendLoading(true);\r\n    setError('');\r\n\r\n    try {\r\n      await authService.generateTwoFactorCode(userId, 'login');\r\n      setSuccess('Verification code resent to your email.');\r\n      setTimeout(() => setSuccess(''), 3000);\r\n    } catch (err: unknown) {\r\n      setError('Failed to resend verification code. Please try again.');\r\n    } finally {\r\n      setResendLoading(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    // Check if we have URL parameters (email verification link) or session storage (manual login)\r\n    const storedUserInfo = sessionStorage.getItem('2fa_login_user');\r\n\r\n    if (!userId && !storedUserInfo) {\r\n      setLoading(false);\r\n      clearAuthContext('No verification session found');\r\n      setUnauthorizedAccess(true);\r\n      setError('No pending 2FA verification found. Please login again.');\r\n      setLoadingMessage('Redirecting to login...');\r\n      setLoading(true);\r\n\r\n      setTimeout(() => {\r\n        router.replace('/customer/auth/login');\r\n      }, 3000);\r\n      return;\r\n    }\r\n\r\n    // If we have session storage (manual login), use that\r\n    if (storedUserInfo && !userId) {\r\n      try {\r\n        const userInfo = JSON.parse(storedUserInfo);\r\n        // Set the userId from session storage for manual verification\r\n        setUserId(userInfo.user_id);\r\n        setLoading(false);\r\n        return;\r\n      } catch (err) {\r\n        setError('Invalid session data. Please login again.');\r\n        setTimeout(() => {\r\n          router.replace('/customer/auth/login');\r\n        }, 3000);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Set up manual input mode directly - all parameters are present\r\n    setLoading(false);\r\n  }, [router, userId, u, clearAuthContext]);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900\">\r\n        <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\r\n          <Loader message={loadingMessage} />\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const alreadyEnabled = success.toLowerCase().includes('enabled');\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900\">\r\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md text-center\">\r\n        <Image src=\"/images/macra-logo.png\" alt=\"MACRA Logo\" width={50} height={50} className=\"mx-auto h-16 w-auto\" />\r\n        <h2 className=\"mt-6 text-3xl font-extrabold text-gray-900 dark:text-white\">\r\n          {success ? (\r\n            <span className=\"text-green-900 dark:text-green-300\">Verification Success!</span>\r\n          ) : error ? (\r\n            <span className=\"text-red-800 dark:text-red-300\">Error</span>\r\n          ) : (\r\n            <span className=\"text-gray-600 dark:text-gray-300\">Two-Factor Authentication</span>\r\n          )}\r\n        </h2>\r\n      </div>\r\n\r\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\r\n        <div className=\"bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10\">\r\n          {error && !alreadyEnabled && (\r\n            <div className=\"flex flex-col flex-auto items-center justify-center\">\r\n              <div className=\"w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center shadow-md\">\r\n                <XCircleIcon className=\"w-10 h-10 animate-pulse text-red-600 dark:text-red-300\" />\r\n              </div>\r\n              <div className=\"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md text-center\">\r\n                {error}\r\n              </div>\r\n              {unauthorizedAccess && (\r\n                <div className=\"mt-4 w-full\">\r\n                  <button\r\n                    onClick={() => router.replace('/customer/auth/login')}\r\n                    className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                  >\r\n                    Go to Login\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n\r\n          {!loading && !success && !unauthorizedAccess && (\r\n            <OTPVerification\r\n              title=\"Verify OTP\"\r\n              description=\"Please enter the 6-digit verification code sent to your email\"\r\n              value={formData.code}\r\n              onChange={(value: string) => handleFormChange('code', value)}\r\n              onSubmit={() => handleManualVerify()}\r\n              error={error}\r\n              loading={verifyLoading}\r\n              submitText=\"Verify Code\"\r\n              loadingText=\"Verifying...\"\r\n              autoSubmit={false}\r\n            >\r\n              <div className=\"mt-4 space-y-3\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={handleResendCode}\r\n                  disabled={verifyLoading || resendLoading}\r\n                  className=\"w-full text-sm text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 transition-colors hover:underline disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                >\r\n                  {resendLoading ? 'Sending...' : 'Resend verification code'}\r\n                </button>\r\n\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => {\r\n                    sessionStorage.removeItem('2fa_login_user');\r\n                    sessionStorage.removeItem('remember_me');\r\n                    router.replace('/customer/auth/login');\r\n                  }}\r\n                  disabled={verifyLoading}\r\n                  className=\"w-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                >\r\n                  Back to Login\r\n                </button>\r\n              </div>\r\n            </OTPVerification>\r\n          )}\r\n\r\n          {(success || alreadyEnabled) && (\r\n            <div className=\"flex flex-col flex-auto items-center justify-center\">\r\n              <div className=\"w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md\">\r\n                <svg\r\n                  className=\"w-8 h-8 text-green-600 dark:text-green-300\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"3\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M5 13l4 4L19 7\" />\r\n                </svg>\r\n              </div>\r\n              <div className=\"text-center text-gray-600 dark:text-gray-400\">\r\n                {success}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAGA;;;AAZA;;;;;;;;;AAce,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,sBAAsB,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;IACR;IAEA,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,GAAG,CAAC,QAAQ;IAC9D,MAAM,IAAI,aAAa,GAAG,CAAC,aAAa;IACxC,MAAM,IAAI,aAAa,GAAG,CAAC,QAAQ;IAEnC,2BAA2B;IAC3B,MAAM,mBAAmB,CAAC,OAAe;QACvC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;QACD,uCAAuC;QACvC,IAAI,OAAO,SAAS;IACtB;IAIA,qDAAqD;IACrD,MAAM,mBAAmB,CAAC;QACxB,QAAQ,IAAI,CAAC,AAAC,0BAAgC,OAAP;QAEvC,wCAAwC;QACxC,wCAAmC;YACjC,eAAe,UAAU,CAAC;YAC1B,eAAe,UAAU,CAAC;YAC1B,eAAe,UAAU,CAAC;YAC1B,eAAe,UAAU,CAAC;QAC5B;QAEA;IACF;IAIA,2CAA2C;IAC3C,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE;YACrC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,MAAM,eAAe;YAE5C,IAAI;gBACF,iBAAiB;gBACjB,SAAS;gBAET,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,MAAM,qIAAA,CAAA,cAAW,CAAC,SAAS,CAAC;oBAClE,SAAS;oBACT,MAAM,SAAS,IAAI,CAAC,IAAI;oBACxB,QAAQ,KAAK,GAAG,8EAA8E;gBAChG;gBAEA,IAAI,gBAAgB,MAAM;oBACxB,WAAW,WAAW;oBAEtB,IAAI,KAAK,kBAAkB,EAAE;wBAC3B,qDAAqD;wBACrD,MAAM,aAAa,eAAe,OAAO,CAAC,mBAAmB;wBAC7D,MAAM,uBAAuB,cAAc,MAAM;wBACjD,wBAAwB;wBACxB,eAAe,UAAU,CAAC;wBAC1B,eAAe,UAAU,CAAC;wBAC1B;qFAAW;gCACT,OAAO,IAAI,CAAC;4BACd;oFAAG;oBACL,OAAO;wBACL,uEAAuE;wBACvE,QAAQ,GAAG,CAAC;wBACZ;qFAAW;gCACT,OAAO,IAAI,CAAC;4BACd;oFAAG;oBACL;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,KAAU;oBAGb,eAEO,gBAIA,gBAEA,gBAIA,oBAAA,gBAkBP;gBAhCJ,IAAI,eAAe;gBAEnB,IAAI,CAAA,gBAAA,2BAAA,gBAAA,IAAK,QAAQ,cAAb,oCAAA,cAAe,MAAM,MAAK,KAAK;oBACjC,eAAe;gBACjB,OAAO,IAAI,CAAA,gBAAA,2BAAA,iBAAA,IAAK,QAAQ,cAAb,qCAAA,eAAe,MAAM,MAAK,KAAK;oBACxC,eAAe;oBACf,uDAAuD;oBACvD,iBAAiB;gBACnB,OAAO,IAAI,CAAA,gBAAA,2BAAA,iBAAA,IAAK,QAAQ,cAAb,qCAAA,eAAe,MAAM,MAAK,KAAK;oBACxC,eAAe;gBACjB,OAAO,IAAI,CAAA,gBAAA,2BAAA,iBAAA,IAAK,QAAQ,cAAb,qCAAA,eAAe,MAAM,MAAK,KAAK;oBACxC,eAAe;oBACf,uDAAuD;oBACvD,iBAAiB;gBACnB,OAAO,IAAI,gBAAA,2BAAA,iBAAA,IAAK,QAAQ,cAAb,sCAAA,qBAAA,eAAe,IAAI,cAAnB,yCAAA,mBAAqB,OAAO,EAAE;oBACvC,eAAe,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO;oBACxC,mCAAmC;oBACnC,IAAI,aAAa,WAAW,GAAG,QAAQ,CAAC,YAAY;wBAClD,0CAA0C;wBAC1C,iBAAiB;oBACnB,OAAO,IAAI,aAAa,WAAW,GAAG,QAAQ,CAAC,cACpC,aAAa,WAAW,GAAG,QAAQ,CAAC,SAAS;wBACtD,oDAAoD;wBACpD,iBAAiB;oBACnB;gBACF,OAAO,IAAI,gBAAA,0BAAA,IAAK,OAAO,EAAE;oBACvB,eAAe,IAAI,OAAO;gBAC5B;gBAEA,SAAS;gBAET,uDAAuD;gBACvD,IAAI,CAAA,gBAAA,2BAAA,iBAAA,IAAK,QAAQ,cAAb,qCAAA,eAAe,MAAM,MAAK,KAAK;oBACjC;iFAAY,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE,MAAM;4BAAG,CAAC;;gBAC5C;YACF,SAAU;gBACR,iBAAiB;YACnB;QACF;gEAAG;QAAC,SAAS,IAAI;QAAE;QAAe;QAAQ;QAAG;QAAwB;QAAQ;KAAiB;IAE9F,MAAM,mBAAmB;QACvB,IAAI,CAAC,QAAQ;QAEb,iBAAiB;QACjB,SAAS;QAET,IAAI;YACF,MAAM,qIAAA,CAAA,cAAW,CAAC,qBAAqB,CAAC,QAAQ;YAChD,WAAW;YACX,WAAW,IAAM,WAAW,KAAK;QACnC,EAAE,OAAO,KAAc;YACrB,SAAS;QACX,SAAU;YACR,iBAAiB;QACnB;IACF;IAIA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,8FAA8F;YAC9F,MAAM,iBAAiB,eAAe,OAAO,CAAC;YAE9C,IAAI,CAAC,UAAU,CAAC,gBAAgB;gBAC9B,WAAW;gBACX,iBAAiB;gBACjB,sBAAsB;gBACtB,SAAS;gBACT,kBAAkB;gBAClB,WAAW;gBAEX;uDAAW;wBACT,OAAO,OAAO,CAAC;oBACjB;sDAAG;gBACH;YACF;YAEA,sDAAsD;YACtD,IAAI,kBAAkB,CAAC,QAAQ;gBAC7B,IAAI;oBACF,MAAM,WAAW,KAAK,KAAK,CAAC;oBAC5B,8DAA8D;oBAC9D,UAAU,SAAS,OAAO;oBAC1B,WAAW;oBACX;gBACF,EAAE,OAAO,KAAK;oBACZ,SAAS;oBACT;2DAAW;4BACT,OAAO,OAAO,CAAC;wBACjB;0DAAG;oBACH;gBACF;YACF;YAEA,iEAAiE;YACjE,WAAW;QACb;0CAAG;QAAC;QAAQ;QAAQ;QAAG;KAAiB;IAExC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+HAAA,CAAA,UAAM;oBAAC,SAAS;;;;;;;;;;;;;;;;IAIzB;IAEA,MAAM,iBAAiB,QAAQ,WAAW,GAAG,QAAQ,CAAC;IACtD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAK;wBAAC,KAAI;wBAAyB,KAAI;wBAAa,OAAO;wBAAI,QAAQ;wBAAI,WAAU;;;;;;kCACtF,6LAAC;wBAAG,WAAU;kCACX,wBACC,6LAAC;4BAAK,WAAU;sCAAqC;;;;;mCACnD,sBACF,6LAAC;4BAAK,WAAU;sCAAiC;;;;;iDAEjD,6LAAC;4BAAK,WAAU;sCAAmC;;;;;;;;;;;;;;;;;0BAKzD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,SAAS,CAAC,gCACT,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,sNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,6LAAC;oCAAI,WAAU;8CACZ;;;;;;gCAEF,oCACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,OAAO,OAAO,CAAC;wCAC9B,WAAU;kDACX;;;;;;;;;;;;;;;;;wBAQR,CAAC,WAAW,CAAC,WAAW,CAAC,oCACxB,6LAAC,iJAAA,CAAA,UAAe;4BACd,OAAM;4BACN,aAAY;4BACZ,OAAO,SAAS,IAAI;4BACpB,UAAU,CAAC,QAAkB,iBAAiB,QAAQ;4BACtD,UAAU,IAAM;4BAChB,OAAO;4BACP,SAAS;4BACT,YAAW;4BACX,aAAY;4BACZ,YAAY;sCAEZ,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU,iBAAiB;wCAC3B,WAAU;kDAET,gBAAgB,eAAe;;;;;;kDAGlC,6LAAC;wCACC,MAAK;wCACL,SAAS;4CACP,eAAe,UAAU,CAAC;4CAC1B,eAAe,UAAU,CAAC;4CAC1B,OAAO,OAAO,CAAC;wCACjB;wCACA,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;;;;;;;wBAON,CAAC,WAAW,cAAc,mBACzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,QAAO;wCACP,aAAY;wCACZ,SAAQ;kDAER,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,GAAE;;;;;;;;;;;;;;;;8CAGzD,6LAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB;GAtSwB;;QACP,qIAAA,CAAA,YAAS;QACmB,kIAAA,CAAA,UAAO;QAY7B,qIAAA,CAAA,kBAAe;;;KAdd", "debugId": null}}, {"offset": {"line": 818, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/node_modules/%40heroicons/react/24/solid/esm/XCircleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction XCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(XCircleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,YAAY,KAIpB,EAAE,MAAM;QAJY,EACnB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJoB;IAKnB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}
'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authService } from '../services/auth.service';
import Cookies from 'js-cookie';
import { startTokenValidationTimer, isTokenExpired } from '../lib/authUtils';
import { AuthResponse, User, RegisterData, UpdateUserData } from '@/types';
import { userService } from '@/services/userService';

interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<User | null>;
  completeTwoFactorLogin: (token: string, userData: User, rememberMe?: boolean) => Promise<void>;
  register: (userData: RegisterData) => Promise<AuthResponse>;
  logout: () => void;
  updateUser: (user: UpdateUserData) => void;
  loading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);
  // Set mounted to true after hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Start token validation timer when mounted and authenticated
  useEffect(() => {
    if (!mounted || !user || !token) return;
    // Start periodic token validation (check every 5 minutes instead of 1 minute)
    const validationTimer = startTokenValidationTimer(300000);
    return () => {
      clearInterval(validationTimer);
    };
  }, [mounted, user, token]);

  useEffect(() => {
    if (!mounted) return;
    // Execute immediately instead of waiting for next render cycle
    initAuth();
  }, [mounted]);

  // Check for existing token on mount with validation
  const initAuth = () => {
    // Get saved data from cookies
    const savedToken = Cookies.get('auth_token');
    const savedUser = Cookies.get('auth_user');
    if (savedToken && savedUser) {
      try {
        const user = JSON.parse(savedUser);
        // Validate token is not expired
        if (!isTokenExpired(savedToken)) {
          setToken(savedToken);
          setUser(user);
          authService.setAuthToken(savedToken);
        } else {
          Cookies.remove('auth_token');
          Cookies.remove('auth_user');
          authService.clearAuthToken();
        }
      } catch (error) {
        console.error('AuthContext: Failed to parse saved user data:', error);
        Cookies.remove('auth_token');
        Cookies.remove('auth_user');
        authService.clearAuthToken();
      }
    } else {
      console.log('AuthContext: No saved authentication data found');
    }
    setLoading(false);
  }

  const login = async (email: string, password: string, rememberMe: boolean = false): Promise<User | null> => {
    try {
      const response: AuthResponse = await authService.login({ email, password });
      // Validate response structure for normal login
      if (!response || !response.user) {
        throw new Error('Invalid response from authentication service');
      }
      // Ensure roles is an array
      const roles = response.user.roles ?? [];

      // Add computed isAdmin property for backward compatibility
      const user = {
        ...response.user,
        roles,
        isAdmin: roles.includes('administrator'),
        isCustomer: roles.includes('customer')
      };
      Cookies.set('auth_user', JSON.stringify(user));
      Cookies.set('auth_token', response.access_token || '');
      initAuth();
      return user;
    } catch (error) {
      console.error('AuthContext: Login failed', error);
      throw error;
    }
  };

  const completeTwoFactorLogin = async (token: string, userData: User, rememberMe: boolean = false): Promise<void> => {
    try {
      // Ensure roles is an array
      const roles: string[] = Array.isArray(userData.roles) ? userData.roles : [];

      // Add computed isAdmin property for backward compatibility
      const user = {
        ...userData,
        roles,
        isAdmin: roles.includes('administrator'),
        isCustomer: roles.includes('customer'),
      };

      if (process.env.NODE_ENV === 'development') {
        console.log('AuthContext: 2FA login successful, setting user', user);
      }
      
      setToken(token);
      setUser(user);

      // Set cookies with appropriate expiration based on rememberMe
      const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise
      Cookies.set('auth_token', token, { expires: cookieExpiration });
      Cookies.set('auth_user', JSON.stringify(user), { expires: cookieExpiration });
      authService.setAuthToken(token);
    } catch (error) {
      console.error('AuthContext: 2FA login completion failed', error);
      throw error;
    }
  };

  const register = async (userData: RegisterData): Promise<AuthResponse> => {
    const result = await authService.register(userData);

    if (process.env.NODE_ENV === 'development') {
      console.log('AuthContext: Registration successful - user should login manually');
    }

    // Don't automatically log in the user after registration
    // User should be redirected to login page to manually log in
    // This follows the requirement that after account creation,
    // users should be redirected to login page, not dashboard

    return result;
  };

  const logout = (): void => {
    if (process.env.NODE_ENV === 'development') {
      console.log('AuthContext: Logging out user');
    }

    // Clear state
    setUser(null);
    setToken(null);

    // Remove from cookies
    Cookies.remove('auth_token');
    Cookies.remove('auth_user');

    // Clear auth service token
    authService.clearAuthToken();

    // Clear any other localStorage items related to auth
    if (mounted && typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_preferences');
      // Clear any cached data
      sessionStorage.clear();
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('AuthContext: Logout complete');
    }
  };

  const updateUser = (updatedUser: UpdateUserData): void => {
    if (process.env.NODE_ENV === 'development') {
      console.log('AuthContext: Updating user', updatedUser);
    }

    // Convert roles to string array if they're objects
    let roles: string[] = [];
    if (updatedUser.roles) {
      roles = updatedUser.roles.map((role: string | { name?: string; role_name?: string }) =>
        typeof role === 'string' ? role : role.name || role.role_name || 'unknown'
      );
    }

    userService.getUserById(updatedUser.user_id).then(user => {
      console.log("===================user")
      console.log(user)
      user.isCustomer =  roles.includes('customer')
      if (process.env.NODE_ENV === 'development') {
        console.log('AuthContext: Setting updated user', updatedUser);
      }
      setUser(user);
      // Update cookies with new user data - use existing token expiration
      const existingToken = Cookies.get('auth_token');
      if (existingToken) {
        // Try to determine original expiration from token or use default
        const cookieExpiration = 1; // Default to 1 day for user updates
        Cookies.set('auth_user', JSON.stringify(updatedUser), { expires: cookieExpiration });
      }
    })
    // Ensure isAdmin property is set for backward compatibility

  };

  const value: AuthContextType = {
    user,
    token,
    login,
    completeTwoFactorLogin,
    register,
    logout,
    updateUser,
    loading: loading || !mounted,
    isAuthenticated: mounted && !!user && !!token,
    
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

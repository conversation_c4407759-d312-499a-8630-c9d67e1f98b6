'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { AuthLayout, StatusMessage, PageTransition } from '@/components/auth';
import { getErrorMessage } from '@/lib';


function LoginForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [fieldErrors, setFieldErrors] = useState<{email?: string; password?: string}>({});
  const [isCustomerPortal, setIsCustomerPortal] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [requires2FA, setRequires2FA] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const [forgotPasswordLoading, setForgotPasswordLoading] = useState(false);
  const [dynamicMessages, setDynamicMessages] = useState<string[]>(['Connecting to staff portal...']);
  const { login, isAuthenticated: staffAuthenticated, loading: staffLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Client-side initialization
  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    // Check if we're on staff portal - fix the logic completely

    const message = searchParams.get('message');
    if (message) {
      setSuccessMessage(message);
    }

    // Add timeout to prevent infinite loading - but don't redirect on timeout
    const loadingTimeout = setTimeout(() => {
      if (loading && !error) {
        setLoading(false);
      }
    }, 10000); // 10 second timeout

    return () => clearTimeout(loadingTimeout);
  }, [searchParams, loading, isClient, error, isCustomerPortal, router]);

  // Redirect if already authenticated - only within staff portal, never to customer
  useEffect(() => {
    // Don't redirect in any of these conditions:
    if (requires2FA) return;
    if (error) return; // Explicit error check first
    if (loading || staffLoading) return; // Don't redirect during loading
    if (!isClient) return; // Wait for client-side hydration
    if (isCustomerPortal) return; // Only redirect in staff portal

    // Only redirect if user is authenticated and no errors
    if (staffAuthenticated && !error) {
      console.log('User already authenticated, redirecting to staff dashboard');
      setIsCustomerPortal(false);
      router.replace('/dashboard');
    }
  }, [requires2FA, isCustomerPortal, staffAuthenticated, staffLoading, loading, router, error, isClient]);

  const validateEmail = (email: string): string | null => {
    if (!email.trim()) {
      return 'Email address is required';
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return 'Please enter a valid email address';
    }
    return null;
  };

  const validatePassword = (password: string): string | null => {
    if (!password) {
      return 'Password is required';
    }

    if (password.length < 8) {
      return 'Password must be at least 8 characters long';
    }

    return null;
  };

  const validateForm = (): string | null => {
    const emailError = validateEmail(email);
    const passwordError = validatePassword(password);

    setFieldErrors({
      email: emailError || undefined,
      password: passwordError || undefined,
    });

    if (emailError) return emailError;
    if (passwordError) return passwordError;

    return null;
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmail(value);

    // Clear field error when user starts typing
    if (fieldErrors.email) {
      setFieldErrors(prev => ({ ...prev, email: undefined }));
    }

    // Clear general error when user starts typing (with slight delay to let user see the error)
    if (error) {
      setTimeout(() => {
        setError('');
      }, 100);
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPassword(value);

    // Clear field error when user starts typing
    if (fieldErrors.password) {
      setFieldErrors(prev => ({ ...prev, password: undefined }));
    }

    // Clear general error when user starts typing (with slight delay to let user see the error)
    if (error) {
      setTimeout(() => {
        setError('');
      }, 100);
    }
  };


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous messages
    setError('');
    setSuccessMessage('');

    // Validate form before submission
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      // Always treat this as staff login since we're on staff portal
      setDynamicMessages(['Verifying your credentials...', 'Please wait...']);
      const response = await login(email.trim().toLowerCase(), password, rememberMe);
      if (response) {
        // Check if 2FA is required
        const requires2FA = response.user.two_factor_enabled && (!response.access_token || response.access_token === '');

        if (requires2FA) {
          setRequires2FA(true);
          setSuccessMessage('Login successful! Two-factor authentication is enabled for your account. Please check your email for the verification code.');
          setDynamicMessages(['Login successful!', 'Sending verification code...', 'Redirecting to 2FA verification...']);

          setTimeout(() => {
            router.replace('/auth/verify-2fa');
          }, 2000);
        } else {
          setSuccessMessage('Login successful! Redirecting to your dashboard...');
          setDynamicMessages(['Login successful!', 'Setting up your session...', 'Redirecting...']);

          setTimeout(() => {
            router.replace('/dashboard');
          }, 1500);
        }
      } else {
        setError('Invalid user session. Please try again.');
        setLoading(false);
        return;
      }
    } catch (err: unknown) {
      // AuthService now provides clean error messages, so we can use them directly
      setLoading(false);
      console.error('Staff login error:', err)
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
      // Return early - stay on login page when authentication fails
      return;
    }
  };

  const handleForgotPasswordClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setForgotPasswordLoading(true);
    setLoadingMessage('Redirecting to forgot password...');
    setTimeout(() => {
      router.push('/auth/forgot-password');
    }, 3000);
  };

  // Show loading while client is initializing, checking authentication, or during login/redirect
  if (!isClient || loading || forgotPasswordLoading) {

    return (
      <PageTransition
        isLoading={true}
        loadingMessage={loadingMessage || (!isClient ? 'Loading...' : 'Signing in...')}
        loadingSubmessage="Please wait while we process your request"
        dynamicMessages={loading ? dynamicMessages : undefined}
        showProgress={loading}
      >
        <div />
      </PageTransition>
    );
  }

  return (
          <AuthLayout
            title="Welcome Back!"
            subtitle={(
              <>
                Staff Portal Access{' '}
                <span className="text-red-600 dark:text-red-400">
                  • Secure Dashboard Login
                </span>
              </>
            )}
            isCustomerPortal={false}
          >
          {error && (
            <StatusMessage
              type="error"
              message={error}
              className="mb-4"
              dismissible={true}
              onDismiss={() => setError('')}
            />
          )}

          {successMessage && (
            <StatusMessage
              type="success"
              message={successMessage}
              className="mb-4"
              dismissible={true}
              onDismiss={() => setSuccessMessage('')}
            />
          )}

          <form className="space-y-6 animate-fadeIn animate-delay-200" onSubmit={handleSubmit}>
            <div className="animate-slideInFromBottom animate-delay-300">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Email address
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={handleEmailChange}
                  className={`appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 field-focus-ring transition-smooth ${
                    fieldErrors.email
                      ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500 animate-shake'
                      : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'
                  }`}
                  placeholder="Enter your email address"
                />
              </div>
              {fieldErrors.email && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center animate-slideInFromTop">
                  <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {fieldErrors.email}
                </p>
              )}
            </div>

            <div className="animate-slideInFromBottom animate-delay-500">
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Password
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={handlePasswordChange}
                  className={`appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 field-focus-ring transition-smooth ${
                    fieldErrors.password
                      ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500 animate-shake'
                      : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'
                  }`}
                  placeholder="Enter your password"
                />
              </div>
              {fieldErrors.password && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center animate-slideInFromTop">
                  <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {fieldErrors.password}
                </p>
              )}
            </div>

            {/* Show Remember Me and Forgot Password only for Staff Portal */}
            {!isCustomerPortal && (
              <div className="flex items-center justify-between animate-fadeIn animate-delay-500">
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    className="h-5 w-5 text-red-600 focus:ring-2 focus:ring-red-500 border-2 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 transition-smooth"
                  />
                  <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                    Remember me
                  </label>
                </div>

                <div className="text-sm">
                  <a
                    href="/auth/forgot-password"
                    onClick={handleForgotPasswordClick}
                    className="font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 transition-colors hover:underline cursor-pointer"
                  >
                    Forgot your password?
                  </a>
                </div>
              </div>
            )}

            <div className="animate-slideInFromBottom animate-delay-500">
              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center items-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 button-hover-lift transition-smooth disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Signing in...
                  </>
                ) : (
                  <>
                    <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3 3v1" />
                    </svg>
                    Sign in
                  </>
                )}
              </button>
            </div>
          </form>
    </AuthLayout>
  );
}

export default function LoginPage() {
  console.log('LoginPage component rendering...');
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"></div>
        <p className="mt-4 text-gray-600">Loading login page...</p>
      </div>
    }>
      <LoginForm />
    </Suspense>
  );
}
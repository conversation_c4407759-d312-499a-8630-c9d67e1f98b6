{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/auth/AuthLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\n\r\ninterface AuthLayoutProps {\r\n  children: React.ReactNode;\r\n  title: string;\r\n  subtitle?: string | React.ReactNode;\r\n  showBackToLogin?: boolean;\r\n  loginPath?: string;\r\n  isCustomerPortal?: boolean;\r\n  className?: string;\r\n}\r\n\r\nconst AuthLayout: React.FC<AuthLayoutProps> = ({\r\n  children,\r\n  title,\r\n  subtitle,\r\n  showBackToLogin = false,\r\n  loginPath,\r\n  isCustomerPortal = false,\r\n  className = ''\r\n}) => {\r\n  const defaultLoginPath = isCustomerPortal ? '/customer/auth/login' : '/auth/login';\r\n  const backToLoginPath = loginPath || defaultLoginPath;\r\n\r\n  return (\r\n    <div className={`min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900 auth-layout ${className}`}>\r\n      {/* Header Section */}\r\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md auth-header\">\r\n        <div className=\"flex justify-center\">\r\n          <Image\r\n            src=\"/images/macra-logo.png\"\r\n            alt=\"MACRA Logo\"\r\n            width={64}\r\n            height={64}\r\n            className=\"h-16 w-auto animate-fadeLoop\"\r\n            priority\r\n          />\r\n        </div>\r\n\r\n        <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100 animate-slideInFromTop animate-delay-100\">\r\n          {title}\r\n        </h2>\r\n\r\n        {subtitle && (\r\n          <p className=\"mt-2 text-center text-sm text-gray-600 dark:text-gray-400 animate-slideInFromTop animate-delay-200\">\r\n            {subtitle}\r\n          </p>\r\n        )}\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\r\n        <div className=\"bg-white dark:bg-gray-800 py-8 px-4 shadow-lg sm:rounded-lg sm:px-10 transition-smooth auth-form\">\r\n          {children}\r\n        </div>\r\n\r\n        {/* Back to Login Link */}\r\n        {showBackToLogin && (\r\n          <div className=\"mt-6 text-center animate-fadeIn animate-delay-300\">\r\n            <Link\r\n              href={backToLoginPath}\r\n              className=\"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 transition-colors duration-200 hover:underline\"\r\n            >\r\n              ← Back to sign in\r\n            </Link>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AuthLayout;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAgBA,MAAM,aAAwC,CAAC,EAC7C,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,kBAAkB,KAAK,EACvB,SAAS,EACT,mBAAmB,KAAK,EACxB,YAAY,EAAE,EACf;IACC,MAAM,mBAAmB,mBAAmB,yBAAyB;IACrE,MAAM,kBAAkB,aAAa;IAErC,qBACE,8OAAC;QAAI,WAAW,CAAC,wGAAwG,EAAE,WAAW;;0BAEpI,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;4BACV,QAAQ;;;;;;;;;;;kCAIZ,8OAAC;wBAAG,WAAU;kCACX;;;;;;oBAGF,0BACC,8OAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;0BAMP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ;;;;;;oBAIF,iCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM;4BACN,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/auth/StatusMessage.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { CheckCircleIcon, ExclamationCircleIcon, InformationCircleIcon } from '@heroicons/react/24/solid';\r\n\r\ninterface StatusMessageProps {\r\n  type: 'success' | 'error' | 'info' | 'warning';\r\n  message: string;\r\n  className?: string;\r\n  showIcon?: boolean;\r\n  dismissible?: boolean;\r\n  onDismiss?: () => void;\r\n}\r\n\r\nconst StatusMessage: React.FC<StatusMessageProps> = ({\r\n  type,\r\n  message,\r\n  className = '',\r\n  showIcon = true,\r\n  dismissible = false,\r\n  onDismiss\r\n}) => {\r\n  const getStatusStyles = () => {\r\n    switch (type) {\r\n      case 'success':\r\n        return {\r\n          container: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-600 dark:text-green-400',\r\n          icon: 'text-green-500 dark:text-green-400'\r\n        };\r\n      case 'error':\r\n        return {\r\n          container: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-600 dark:text-red-400',\r\n          icon: 'text-red-500 dark:text-red-400'\r\n        };\r\n      case 'warning':\r\n        return {\r\n          container: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-600 dark:text-yellow-400',\r\n          icon: 'text-yellow-500 dark:text-yellow-400'\r\n        };\r\n      case 'info':\r\n      default:\r\n        return {\r\n          container: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-600 dark:text-blue-400',\r\n          icon: 'text-blue-500 dark:text-blue-400'\r\n        };\r\n    }\r\n  };\r\n\r\n  const getIcon = () => {\r\n    switch (type) {\r\n      case 'success':\r\n        return <CheckCircleIcon className=\"h-5 w-5\" />;\r\n      case 'error':\r\n      case 'warning':\r\n        return <ExclamationCircleIcon className=\"h-5 w-5\" />;\r\n      case 'info':\r\n      default:\r\n        return <InformationCircleIcon className=\"h-5 w-5\" />;\r\n    }\r\n  };\r\n\r\n  const styles = getStatusStyles();\r\n\r\n  return (\r\n    <div className={`border px-4 py-3 rounded-md transition-smooth status-message-enter ${styles.container} ${className} ${type === 'error' ? 'animate-shake' : ''}`}>\r\n      <div className=\"flex items-start\">\r\n        {showIcon && (\r\n          <div className={`flex-shrink-0 ${styles.icon} animate-scaleIn`}>\r\n            {getIcon()}\r\n          </div>\r\n        )}\r\n        <div className={`${showIcon ? 'ml-3' : ''} flex-1 animate-slideInFromBottom animate-delay-100`}>\r\n          <p className=\"text-sm font-medium\">{message}</p>\r\n        </div>\r\n        {dismissible && onDismiss && (\r\n          <button\r\n            onClick={onDismiss}\r\n            className=\"ml-3 flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors hover:scale-110 transform duration-200\"\r\n          >\r\n            <span className=\"sr-only\">Dismiss</span>\r\n            <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n              <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StatusMessage;\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAHA;;;AAcA,MAAM,gBAA8C,CAAC,EACnD,IAAI,EACJ,OAAO,EACP,YAAY,EAAE,EACd,WAAW,IAAI,EACf,cAAc,KAAK,EACnB,SAAS,EACV;IACC,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;gBACR;YACF,KAAK;YACL;gBACE,OAAO;oBACL,WAAW;oBACX,MAAM;gBACR;QACJ;IACF;IAEA,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,uOAAA,CAAA,wBAAqB;oBAAC,WAAU;;;;;;YAC1C,KAAK;YACL;gBACE,qBAAO,8OAAC,uOAAA,CAAA,wBAAqB;oBAAC,WAAU;;;;;;QAC5C;IACF;IAEA,MAAM,SAAS;IAEf,qBACE,8OAAC;QAAI,WAAW,CAAC,mEAAmE,EAAE,OAAO,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,SAAS,UAAU,kBAAkB,IAAI;kBAC9J,cAAA,8OAAC;YAAI,WAAU;;gBACZ,0BACC,8OAAC;oBAAI,WAAW,CAAC,cAAc,EAAE,OAAO,IAAI,CAAC,gBAAgB,CAAC;8BAC3D;;;;;;8BAGL,8OAAC;oBAAI,WAAW,GAAG,WAAW,SAAS,GAAG,mDAAmD,CAAC;8BAC5F,cAAA,8OAAC;wBAAE,WAAU;kCAAuB;;;;;;;;;;;gBAErC,eAAe,2BACd,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,8OAAC;4BAAI,WAAU;4BAAU,SAAQ;4BAAY,MAAK;sCAChD,cAAA,8OAAC;gCAAK,UAAS;gCAAU,GAAE;gCAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvP;uCAEe", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/auth/LoadingState.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Image from 'next/image';\r\n\r\ninterface LoadingStateProps {\r\n  message?: string;\r\n  submessage?: string;\r\n  showProgress?: boolean;\r\n  progress?: number;\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  dynamicMessages?: string[];\r\n  messageInterval?: number;\r\n}\r\n\r\nconst LoadingState: React.FC<LoadingStateProps> = ({\r\n  message = 'Loading...',\r\n  submessage,\r\n  showProgress = false,\r\n  progress = 0,\r\n  size = 'md',\r\n  className = '',\r\n  dynamicMessages = [],\r\n  messageInterval = 2000\r\n}) => {\r\n  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);\r\n  const [displayMessage, setDisplayMessage] = useState(message);\r\n\r\n  // Handle dynamic message rotation\r\n  useEffect(() => {\r\n    if (dynamicMessages.length > 0) {\r\n      const interval = setInterval(() => {\r\n        setCurrentMessageIndex((prev) => (prev + 1) % dynamicMessages.length);\r\n      }, messageInterval);\r\n\r\n      return () => clearInterval(interval);\r\n    }\r\n  }, [dynamicMessages, messageInterval]);\r\n\r\n  // Update display message when dynamic messages change\r\n  useEffect(() => {\r\n    if (dynamicMessages.length > 0) {\r\n      setDisplayMessage(dynamicMessages[currentMessageIndex]);\r\n    } else {\r\n      setDisplayMessage(message);\r\n    }\r\n  }, [currentMessageIndex, dynamicMessages, message]);\r\n\r\n  const getSizeClasses = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return {\r\n          container: 'w-12 h-12',\r\n          logo: 'h-6 w-6',\r\n          text: 'text-sm'\r\n        };\r\n      case 'lg':\r\n        return {\r\n          container: 'w-24 h-24',\r\n          logo: 'h-12 w-12',\r\n          text: 'text-lg'\r\n        };\r\n      case 'md':\r\n      default:\r\n        return {\r\n          container: 'w-20 h-20',\r\n          logo: 'h-10 w-10',\r\n          text: 'text-base'\r\n        };\r\n    }\r\n  };\r\n\r\n  const sizeClasses = getSizeClasses();\r\n\r\n  return (\r\n    <div className={`text-center ${className}`}>\r\n      {/* Loading Spinner with Logo */}\r\n      <div className={`relative ${sizeClasses.container} mx-auto`}>\r\n        {/* Animated Spinner */}\r\n        <svg\r\n          className=\"absolute inset-0 animate-spin\"\r\n          viewBox=\"0 0 50 50\"\r\n          fill=\"none\"\r\n        >\r\n          <defs>\r\n            <linearGradient id=\"fadeGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n              <stop offset=\"0%\" stopColor=\"#dc2626\" stopOpacity=\"0\" />\r\n              <stop offset=\"20%\" stopColor=\"#dc2626\" stopOpacity=\"1\" />\r\n              <stop offset=\"80%\" stopColor=\"#dc2626\" stopOpacity=\"1\" />\r\n              <stop offset=\"100%\" stopColor=\"#dc2626\" stopOpacity=\"0\" />\r\n            </linearGradient>\r\n          </defs>\r\n\r\n          <circle\r\n            cx=\"25\"\r\n            cy=\"25\"\r\n            r=\"20\"\r\n            stroke=\"rgba(255, 255, 255, 0.1)\"\r\n            strokeWidth=\"2\"\r\n          />\r\n          <circle\r\n            cx=\"25\"\r\n            cy=\"25\"\r\n            r=\"20\"\r\n            stroke=\"url(#fadeGradient)\"\r\n            strokeWidth=\"2\"\r\n            strokeDasharray=\"70\"\r\n            strokeDashoffset=\"10\"\r\n            fill=\"none\"\r\n          />\r\n        </svg>\r\n\r\n        {/* MACRA Logo */}\r\n        <Image\r\n          src=\"/images/macra-logo.png\"\r\n          alt=\"MACRA Logo\"\r\n          width={40}\r\n          height={40}\r\n          className={`object-contain absolute inset-0 ${sizeClasses.logo} m-auto animate-pulse`}\r\n          priority\r\n        />\r\n      </div>\r\n\r\n      {/* Progress Bar */}\r\n      {showProgress && (\r\n        <div className=\"mt-4 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\r\n          <div\r\n            className=\"bg-red-600 h-2 rounded-full transition-all duration-300 ease-out\"\r\n            style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}\r\n          />\r\n        </div>\r\n      )}\r\n\r\n      {/* Loading Message */}\r\n      <div className=\"mt-4 space-y-1\">\r\n        <p className={`text-gray-600 dark:text-gray-400 font-medium ${sizeClasses.text} transition-opacity duration-300`}>\r\n          {displayMessage}\r\n        </p>\r\n        {submessage && (\r\n          <p className=\"text-sm text-gray-500 dark:text-gray-500\">\r\n            {submessage}\r\n          </p>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoadingState;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAgBA,MAAM,eAA4C,CAAC,EACjD,UAAU,YAAY,EACtB,UAAU,EACV,eAAe,KAAK,EACpB,WAAW,CAAC,EACZ,OAAO,IAAI,EACX,YAAY,EAAE,EACd,kBAAkB,EAAE,EACpB,kBAAkB,IAAI,EACvB;IACC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,MAAM,WAAW,YAAY;gBAC3B,uBAAuB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,gBAAgB,MAAM;YACtE,GAAG;YAEH,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;QAAiB;KAAgB;IAErC,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,kBAAkB,eAAe,CAAC,oBAAoB;QACxD,OAAO;YACL,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAqB;QAAiB;KAAQ;IAElD,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,MAAM;gBACR;YACF,KAAK;YACL;gBACE,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,MAAM;gBACR;QACJ;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC;QAAI,WAAW,CAAC,YAAY,EAAE,WAAW;;0BAExC,8OAAC;gBAAI,WAAW,CAAC,SAAS,EAAE,YAAY,SAAS,CAAC,QAAQ,CAAC;;kCAEzD,8OAAC;wBACC,WAAU;wBACV,SAAQ;wBACR,MAAK;;0CAEL,8OAAC;0CACC,cAAA,8OAAC;oCAAe,IAAG;oCAAe,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC7D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;4CAAU,aAAY;;;;;;sDAClD,8OAAC;4CAAK,QAAO;4CAAM,WAAU;4CAAU,aAAY;;;;;;sDACnD,8OAAC;4CAAK,QAAO;4CAAM,WAAU;4CAAU,aAAY;;;;;;sDACnD,8OAAC;4CAAK,QAAO;4CAAO,WAAU;4CAAU,aAAY;;;;;;;;;;;;;;;;;0CAIxD,8OAAC;gCACC,IAAG;gCACH,IAAG;gCACH,GAAE;gCACF,QAAO;gCACP,aAAY;;;;;;0CAEd,8OAAC;gCACC,IAAG;gCACH,IAAG;gCACH,GAAE;gCACF,QAAO;gCACP,aAAY;gCACZ,iBAAgB;gCAChB,kBAAiB;gCACjB,MAAK;;;;;;;;;;;;kCAKT,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAW,CAAC,gCAAgC,EAAE,YAAY,IAAI,CAAC,qBAAqB,CAAC;wBACrF,QAAQ;;;;;;;;;;;;YAKX,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC;oBAAC;;;;;;;;;;;0BAMjE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAW,CAAC,6CAA6C,EAAE,YAAY,IAAI,CAAC,gCAAgC,CAAC;kCAC7G;;;;;;oBAEF,4BACC,8OAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/auth/PageTransition.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport LoadingState from './LoadingState';\r\n\r\ninterface PageTransitionProps {\r\n  children: React.ReactNode;\r\n  isLoading?: boolean;\r\n  loadingMessage?: string;\r\n  loadingSubmessage?: string;\r\n  redirectTo?: string;\r\n  redirectDelay?: number;\r\n  showProgress?: boolean;\r\n  dynamicMessages?: string[];\r\n  className?: string;\r\n}\r\n\r\nconst PageTransition: React.FC<PageTransitionProps> = ({\r\n  children,\r\n  isLoading = false,\r\n  loadingMessage = 'Loading...',\r\n  loadingSubmessage,\r\n  redirectTo,\r\n  redirectDelay = 2000,\r\n  showProgress = false,\r\n  dynamicMessages = [],\r\n  className = ''\r\n}) => {\r\n  const router = useRouter();\r\n  const [progress, setProgress] = useState(0);\r\n  const [isRedirecting, setIsRedirecting] = useState(false);\r\n\r\n  // Handle redirect with progress\r\n  useEffect(() => {\r\n    if (redirectTo && !isRedirecting) {\r\n      setIsRedirecting(true);\r\n      \r\n      if (showProgress) {\r\n        const progressInterval = setInterval(() => {\r\n          setProgress((prev) => {\r\n            if (prev >= 100) {\r\n              clearInterval(progressInterval);\r\n              return 100;\r\n            }\r\n            return prev + (100 / (redirectDelay / 100));\r\n          });\r\n        }, 100);\r\n\r\n        const redirectTimeout = setTimeout(() => {\r\n          router.push(redirectTo);\r\n        }, redirectDelay);\r\n\r\n        return () => {\r\n          clearInterval(progressInterval);\r\n          clearTimeout(redirectTimeout);\r\n        };\r\n      } else {\r\n        const redirectTimeout = setTimeout(() => {\r\n          router.push(redirectTo);\r\n        }, redirectDelay);\r\n\r\n        return () => clearTimeout(redirectTimeout);\r\n      }\r\n    }\r\n  }, [redirectTo, redirectDelay, router, showProgress, isRedirecting]);\r\n\r\n  if (isLoading || isRedirecting) {\r\n    return (\r\n      <div className={`min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 ${className}`}>\r\n        <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\r\n          <LoadingState\r\n            message={loadingMessage}\r\n            submessage={loadingSubmessage}\r\n            showProgress={showProgress}\r\n            progress={progress}\r\n            size=\"lg\"\r\n            dynamicMessages={dynamicMessages}\r\n          />\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={`transition-all duration-300 ease-in-out ${className}`}>\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PageTransition;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAkBA,MAAM,iBAAgD,CAAC,EACrD,QAAQ,EACR,YAAY,KAAK,EACjB,iBAAiB,YAAY,EAC7B,iBAAiB,EACjB,UAAU,EACV,gBAAgB,IAAI,EACpB,eAAe,KAAK,EACpB,kBAAkB,EAAE,EACpB,YAAY,EAAE,EACf;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,CAAC,eAAe;YAChC,iBAAiB;YAEjB,IAAI,cAAc;gBAChB,MAAM,mBAAmB,YAAY;oBACnC,YAAY,CAAC;wBACX,IAAI,QAAQ,KAAK;4BACf,cAAc;4BACd,OAAO;wBACT;wBACA,OAAO,OAAQ,MAAM,CAAC,gBAAgB,GAAG;oBAC3C;gBACF,GAAG;gBAEH,MAAM,kBAAkB,WAAW;oBACjC,OAAO,IAAI,CAAC;gBACd,GAAG;gBAEH,OAAO;oBACL,cAAc;oBACd,aAAa;gBACf;YACF,OAAO;gBACL,MAAM,kBAAkB,WAAW;oBACjC,OAAO,IAAI,CAAC;gBACd,GAAG;gBAEH,OAAO,IAAM,aAAa;YAC5B;QACF;IACF,GAAG;QAAC;QAAY;QAAe;QAAQ;QAAc;KAAc;IAEnE,IAAI,aAAa,eAAe;QAC9B,qBACE,8OAAC;YAAI,WAAW,CAAC,0EAA0E,EAAE,WAAW;sBACtG,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0IAAA,CAAA,UAAY;oBACX,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,UAAU;oBACV,MAAK;oBACL,iBAAiB;;;;;;;;;;;;;;;;IAK3B;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,wCAAwC,EAAE,WAAW;kBACnE;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/auth/SuccessState.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { CheckCircleIcon, EnvelopeIcon } from '@heroicons/react/24/solid';\r\n\r\ninterface SuccessStateProps {\r\n  title: string;\r\n  message: string;\r\n  submessage?: string;\r\n  showEmailIcon?: boolean;\r\n  actionText?: string;\r\n  actionHref?: string;\r\n  secondaryActionText?: string;\r\n  secondaryActionHref?: string;\r\n  autoRedirect?: boolean;\r\n  redirectDelay?: number;\r\n  redirectMessage?: string;\r\n  className?: string;\r\n}\r\n\r\nconst SuccessState: React.FC<SuccessStateProps> = ({\r\n  title,\r\n  message,\r\n  submessage,\r\n  showEmailIcon = false,\r\n  actionText,\r\n  actionHref,\r\n  secondaryActionText,\r\n  secondaryActionHref,\r\n  autoRedirect = false,\r\n  redirectDelay = 5000,\r\n  redirectMessage,\r\n  className = ''\r\n}) => {\r\n  const [countdown, setCountdown] = useState(Math.ceil(redirectDelay / 1000));\r\n\r\n  useEffect(() => {\r\n    if (autoRedirect && actionHref) {\r\n      const timer = setInterval(() => {\r\n        setCountdown((prev) => {\r\n          if (prev <= 1) {\r\n            clearInterval(timer);\r\n            window.location.href = actionHref;\r\n            return 0;\r\n          }\r\n          return prev - 1;\r\n        });\r\n      }, 1000);\r\n\r\n      return () => clearInterval(timer);\r\n    }\r\n  }, [autoRedirect, actionHref]);\r\n\r\n  return (\r\n    <div className={`flex flex-col items-center justify-center text-center ${className}`}>\r\n      {/* Success Icon */}\r\n      <div className=\"w-16 h-16 mb-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center shadow-lg success-icon\">\r\n        {showEmailIcon ? (\r\n          <EnvelopeIcon className=\"w-8 h-8 text-green-600 dark:text-green-300\" />\r\n        ) : (\r\n          <CheckCircleIcon className=\"w-8 h-8 text-green-600 dark:text-green-300\" />\r\n        )}\r\n      </div>\r\n\r\n      {/* Title */}\r\n      <h3 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3 success-content\">\r\n        {title}\r\n      </h3>\r\n\r\n      {/* Main Message */}\r\n      <p className=\"text-gray-600 dark:text-gray-400 mb-2 max-w-md success-content\">\r\n        {message}\r\n      </p>\r\n\r\n      {/* Submessage */}\r\n      {submessage && (\r\n        <p className=\"text-sm text-gray-500 dark:text-gray-500 mb-6 max-w-md success-content\">\r\n          {submessage}\r\n        </p>\r\n      )}\r\n\r\n      {/* Auto Redirect Message */}\r\n      {autoRedirect && redirectMessage && countdown > 0 && (\r\n        <div className=\"mb-6 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg animate-pulse\">\r\n          <p className=\"text-sm text-blue-600 dark:text-blue-400\">\r\n            {redirectMessage.replace('{countdown}', countdown.toString())}\r\n          </p>\r\n        </div>\r\n      )}\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"flex flex-col sm:flex-row gap-3 w-full max-w-sm success-content\">\r\n        {actionText && actionHref && (\r\n          <Link\r\n            href={actionHref}\r\n            className=\"flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 button-hover-lift transition-smooth\"\r\n          >\r\n            {actionText}\r\n          </Link>\r\n        )}\r\n\r\n        {secondaryActionText && secondaryActionHref && (\r\n          <Link\r\n            href={secondaryActionHref}\r\n            className=\"flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 button-hover-lift transition-smooth\"\r\n          >\r\n            {secondaryActionText}\r\n          </Link>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SuccessState;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAqBA,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,OAAO,EACP,UAAU,EACV,gBAAgB,KAAK,EACrB,UAAU,EACV,UAAU,EACV,mBAAmB,EACnB,mBAAmB,EACnB,eAAe,KAAK,EACpB,gBAAgB,IAAI,EACpB,eAAe,EACf,YAAY,EAAE,EACf;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,IAAI,CAAC,gBAAgB;IAErE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,YAAY;YAC9B,MAAM,QAAQ,YAAY;gBACxB,aAAa,CAAC;oBACZ,IAAI,QAAQ,GAAG;wBACb,cAAc;wBACd,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACvB,OAAO;oBACT;oBACA,OAAO,OAAO;gBAChB;YACF,GAAG;YAEH,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;QAAc;KAAW;IAE7B,qBACE,8OAAC;QAAI,WAAW,CAAC,sDAAsD,EAAE,WAAW;;0BAElF,8OAAC;gBAAI,WAAU;0BACZ,8BACC,8OAAC,qNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;6EAExB,8OAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;0BAK/B,8OAAC;gBAAG,WAAU;0BACX;;;;;;0BAIH,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIF,4BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAKJ,gBAAgB,mBAAmB,YAAY,mBAC9C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BACV,gBAAgB,OAAO,CAAC,eAAe,UAAU,QAAQ;;;;;;;;;;;0BAMhE,8OAAC;gBAAI,WAAU;;oBACZ,cAAc,4BACb,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAM;wBACN,WAAU;kCAET;;;;;;oBAIJ,uBAAuB,qCACtB,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAM;wBACN,WAAU;kCAET;;;;;;;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/auth/index.ts"], "sourcesContent": ["export { default as AuthLayout } from './AuthLayout';\r\nexport { default as StatusMessage } from './StatusMessage';\r\nexport { default as LoadingState } from './LoadingState';\r\nexport { default as PageTransition } from './PageTransition';\r\nexport { default as SuccessState } from './SuccessState';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/auth/login/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, Suspense } from 'react';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { AuthLayout, StatusMessage, PageTransition } from '@/components/auth';\r\nimport { getErrorMessage } from '@/lib';\r\n\r\n\r\nfunction LoginForm() {\r\n  const [email, setEmail] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const [rememberMe, setRememberMe] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [fieldErrors, setFieldErrors] = useState<{email?: string; password?: string}>({});\r\n  const [isCustomerPortal, setIsCustomerPortal] = useState(false);\r\n  const [isClient, setIsClient] = useState(false);\r\n  const [requires2FA, setRequires2FA] = useState(false);\r\n  const [loadingMessage, setLoadingMessage] = useState('');\r\n  const [forgotPasswordLoading, setForgotPasswordLoading] = useState(false);\r\n  const [dynamicMessages, setDynamicMessages] = useState<string[]>(['Connecting to staff portal...']);\r\n  const { login, isAuthenticated, loading: staffLoading } = useAuth();\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n\r\n  // Client-side initialization\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Check if we're on staff portal - fix the logic completely\r\n\r\n    const message = searchParams.get('message');\r\n    if (message) {\r\n      setSuccessMessage(message);\r\n    }\r\n\r\n    // Add timeout to prevent infinite loading - but don't redirect on timeout\r\n    const loadingTimeout = setTimeout(() => {\r\n      if (loading && !error) {\r\n        setLoading(false);\r\n      }\r\n    }, 10000); // 10 second timeout\r\n\r\n    return () => clearTimeout(loadingTimeout);\r\n  }, [searchParams, loading, isClient, error, isCustomerPortal, router]);\r\n\r\n  // Redirect if already authenticated - only within staff portal, never to customer\r\n  useEffect(() => {\r\n    // Don't redirect in any of these conditions:\r\n    if (requires2FA) return;\r\n    if (error) return; // Explicit error check first\r\n    if (loading || staffLoading) return; // Don't redirect during loading\r\n    if (!isClient) return; // Wait for client-side hydration\r\n    if (isCustomerPortal) return; // Only redirect in staff portal\r\n\r\n    // Only redirect if user is authenticated and no errors\r\n    if (isAuthenticated && !error) {\r\n      console.log('User already authenticated, redirecting to staff dashboard');\r\n      setIsCustomerPortal(false);\r\n      router.replace('/dashboard');\r\n    }\r\n  }, [requires2FA, isCustomerPortal, isAuthenticated, staffLoading, loading, router, error, isClient]);\r\n\r\n  const validateEmail = (email: string): string | null => {\r\n    if (!email.trim()) {\r\n      return 'Email address is required';\r\n    }\r\n\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    if (!emailRegex.test(email)) {\r\n      return 'Please enter a valid email address';\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const validatePassword = (password: string): string | null => {\r\n    if (!password) {\r\n      return 'Password is required';\r\n    }\r\n\r\n    if (password.length < 8) {\r\n      return 'Password must be at least 8 characters long';\r\n    }\r\n\r\n    return null;\r\n  };\r\n\r\n  const validateForm = (): string | null => {\r\n    const emailError = validateEmail(email);\r\n    const passwordError = validatePassword(password);\r\n\r\n    setFieldErrors({\r\n      email: emailError || undefined,\r\n      password: passwordError || undefined,\r\n    });\r\n\r\n    if (emailError) return emailError;\r\n    if (passwordError) return passwordError;\r\n\r\n    return null;\r\n  };\r\n\r\n  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const value = e.target.value;\r\n    setEmail(value);\r\n\r\n    // Clear field error when user starts typing\r\n    if (fieldErrors.email) {\r\n      setFieldErrors(prev => ({ ...prev, email: undefined }));\r\n    }\r\n\r\n    // Clear general error when user starts typing (with slight delay to let user see the error)\r\n    if (error) {\r\n      setTimeout(() => {\r\n        setError('');\r\n      }, 100);\r\n    }\r\n  };\r\n\r\n  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const value = e.target.value;\r\n    setPassword(value);\r\n\r\n    // Clear field error when user starts typing\r\n    if (fieldErrors.password) {\r\n      setFieldErrors(prev => ({ ...prev, password: undefined }));\r\n    }\r\n\r\n    // Clear general error when user starts typing (with slight delay to let user see the error)\r\n    if (error) {\r\n      setTimeout(() => {\r\n        setError('');\r\n      }, 100);\r\n    }\r\n  };\r\n\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Clear previous messages\r\n    setError('');\r\n    setSuccessMessage('');\r\n\r\n    // Validate form before submission\r\n    const validationError = validateForm();\r\n    if (validationError) {\r\n      setError(validationError);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      // Always treat this as staff login since we're on staff portal\r\n      setDynamicMessages(['Verifying your credentials...', 'Please wait...']);\r\n      const response = await login(email.trim().toLowerCase(), password, rememberMe);\r\n      if (response) {\r\n        // Check if 2FA is required\r\n        const requires2FA = response.user.two_factor_enabled;\r\n        if (requires2FA) {\r\n          setRequires2FA(true);\r\n          setSuccessMessage('Login successful! Two-factor authentication is enabled for your account. Please check your email for the verification code.');\r\n          setDynamicMessages(['Login successful!', 'Sending verification code...', 'Redirecting to 2FA verification...']);\r\n\r\n          // Store user info for manual verification\r\n          sessionStorage.setItem('2fa_login_user', JSON.stringify(response.user));\r\n\r\n          setTimeout(() => {\r\n            router.replace('/auth/verify-2fa');\r\n          }, 1000);\r\n        } else {\r\n          setSuccessMessage('Login successful! Redirecting to your dashboard...');\r\n          setDynamicMessages(['Login successful!', 'Setting up your session...', 'Redirecting...']);\r\n\r\n          setTimeout(() => {\r\n            router.replace('/dashboard');\r\n          }, 1000);\r\n        }\r\n      } else {\r\n        setError('Invalid user session. Please try again.');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n    } catch (err: unknown) {\r\n      // AuthService now provides clean error messages, so we can use them directly\r\n      setLoading(false);\r\n      console.error('Staff login error:', err)\r\n      const errorMessage = getErrorMessage(err);\r\n      setError(errorMessage);\r\n      // Return early - stay on login page when authentication fails\r\n      return;\r\n    }\r\n  };\r\n\r\n  const handleForgotPasswordClick = (e: React.MouseEvent) => {\r\n    e.preventDefault();\r\n    setForgotPasswordLoading(true);\r\n    setLoadingMessage('Redirecting to forgot password...');\r\n    setTimeout(() => {\r\n      router.push('/auth/forgot-password');\r\n    }, 3000);\r\n  };\r\n\r\n  // Show loading while client is initializing, checking authentication, or during login/redirect\r\n  if (!isClient || loading || forgotPasswordLoading) {\r\n\r\n    return (\r\n      <PageTransition\r\n        isLoading={true}\r\n        loadingMessage={loadingMessage || (!isClient ? 'Loading...' : 'Signing in...')}\r\n        loadingSubmessage=\"Please wait while we process your request\"\r\n        dynamicMessages={loading ? dynamicMessages : undefined}\r\n        showProgress={loading}\r\n      >\r\n        <div />\r\n      </PageTransition>\r\n    );\r\n  }\r\n\r\n  return (\r\n          <AuthLayout\r\n            title=\"Welcome Back!\"\r\n            subtitle={(\r\n              <>\r\n                Staff Portal Access{' '}\r\n                <span className=\"text-red-600 dark:text-red-400\">\r\n                  • Secure Dashboard Login\r\n                </span>\r\n              </>\r\n            )}\r\n            isCustomerPortal={false}\r\n          >\r\n          {error && (\r\n            <StatusMessage\r\n              type=\"error\"\r\n              message={error}\r\n              className=\"mb-4\"\r\n              dismissible={true}\r\n              onDismiss={() => setError('')}\r\n            />\r\n          )}\r\n\r\n          {successMessage && (\r\n            <StatusMessage\r\n              type=\"success\"\r\n              message={successMessage}\r\n              className=\"mb-4\"\r\n              dismissible={true}\r\n              onDismiss={() => setSuccessMessage('')}\r\n            />\r\n          )}\r\n\r\n          <form className=\"space-y-6 animate-fadeIn animate-delay-200\" onSubmit={handleSubmit}>\r\n            <div className=\"animate-slideInFromBottom animate-delay-300\">\r\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                Email address\r\n              </label>\r\n              <div className=\"mt-1\">\r\n                <input\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  type=\"email\"\r\n                  autoComplete=\"email\"\r\n                  required\r\n                  value={email}\r\n                  onChange={handleEmailChange}\r\n                  className={`appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 field-focus-ring transition-smooth ${\r\n                    fieldErrors.email\r\n                      ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500 animate-shake'\r\n                      : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'\r\n                  }`}\r\n                  placeholder=\"Enter your email address\"\r\n                />\r\n              </div>\r\n              {fieldErrors.email && (\r\n                <p className=\"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center animate-slideInFromTop\">\r\n                  <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                  {fieldErrors.email}\r\n                </p>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"animate-slideInFromBottom animate-delay-500\">\r\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                Password\r\n              </label>\r\n              <div className=\"mt-1\">\r\n                <input\r\n                  id=\"password\"\r\n                  name=\"password\"\r\n                  type=\"password\"\r\n                  autoComplete=\"current-password\"\r\n                  required\r\n                  value={password}\r\n                  onChange={handlePasswordChange}\r\n                  className={`appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 field-focus-ring transition-smooth ${\r\n                    fieldErrors.password\r\n                      ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500 animate-shake'\r\n                      : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'\r\n                  }`}\r\n                  placeholder=\"Enter your password\"\r\n                />\r\n              </div>\r\n              {fieldErrors.password && (\r\n                <p className=\"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center animate-slideInFromTop\">\r\n                  <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                  {fieldErrors.password}\r\n                </p>\r\n              )}\r\n            </div>\r\n\r\n            {/* Show Remember Me and Forgot Password only for Staff Portal */}\r\n            {!isCustomerPortal && (\r\n              <div className=\"flex items-center justify-between animate-fadeIn animate-delay-500\">\r\n                <div className=\"flex items-center\">\r\n                  <input\r\n                    id=\"remember-me\"\r\n                    name=\"remember-me\"\r\n                    type=\"checkbox\"\r\n                    checked={rememberMe}\r\n                    onChange={(e) => setRememberMe(e.target.checked)}\r\n                    className=\"h-5 w-5 text-red-600 focus:ring-2 focus:ring-red-500 border-2 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 transition-smooth\"\r\n                  />\r\n                  <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-900 dark:text-gray-100\">\r\n                    Remember me\r\n                  </label>\r\n                </div>\r\n\r\n                <div className=\"text-sm\">\r\n                  <a\r\n                    href=\"/auth/forgot-password\"\r\n                    onClick={handleForgotPasswordClick}\r\n                    className=\"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 transition-colors hover:underline cursor-pointer\"\r\n                  >\r\n                    Forgot your password?\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"animate-slideInFromBottom animate-delay-500\">\r\n              <button\r\n                type=\"submit\"\r\n                disabled={loading}\r\n                className=\"w-full flex justify-center items-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 button-hover-lift transition-smooth disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                {loading ? (\r\n                  <>\r\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                    Signing in...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <svg className=\"h-4 w-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3 3v1\" />\r\n                    </svg>\r\n                    Sign in\r\n                  </>\r\n                )}\r\n              </button>\r\n            </div>\r\n          </form>\r\n    </AuthLayout>\r\n  );\r\n}\r\n\r\nexport default function LoginPage() {\r\n  console.log('LoginPage component rendering...');\r\n  return (\r\n    <Suspense fallback={\r\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\r\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600\"></div>\r\n        <p className=\"mt-4 text-gray-600\">Loading login page...</p>\r\n      </div>\r\n    }>\r\n      <LoginForm />\r\n    </Suspense>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AANA;;;;;;;AASA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuC,CAAC;IACrF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAAgC;IAClG,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,SAAS,YAAY,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4DAA4D;QAE5D,MAAM,UAAU,aAAa,GAAG,CAAC;QACjC,IAAI,SAAS;YACX,kBAAkB;QACpB;QAEA,0EAA0E;QAC1E,MAAM,iBAAiB,WAAW;YAChC,IAAI,WAAW,CAAC,OAAO;gBACrB,WAAW;YACb;QACF,GAAG,QAAQ,oBAAoB;QAE/B,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAc;QAAS;QAAU;QAAO;QAAkB;KAAO;IAErE,kFAAkF;IAClF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6CAA6C;QAC7C,IAAI,aAAa;QACjB,IAAI,OAAO,QAAQ,6BAA6B;QAChD,IAAI,WAAW,cAAc,QAAQ,gCAAgC;QACrE,IAAI,CAAC,UAAU,QAAQ,iCAAiC;QACxD,IAAI,kBAAkB,QAAQ,gCAAgC;QAE9D,uDAAuD;QACvD,IAAI,mBAAmB,CAAC,OAAO;YAC7B,QAAQ,GAAG,CAAC;YACZ,oBAAoB;YACpB,OAAO,OAAO,CAAC;QACjB;IACF,GAAG;QAAC;QAAa;QAAkB;QAAiB;QAAc;QAAS;QAAQ;QAAO;KAAS;IAEnG,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,OAAO;QACT;QAEA,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;YAC3B,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,UAAU;YACb,OAAO;QACT;QAEA,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,MAAM,aAAa,cAAc;QACjC,MAAM,gBAAgB,iBAAiB;QAEvC,eAAe;YACb,OAAO,cAAc;YACrB,UAAU,iBAAiB;QAC7B;QAEA,IAAI,YAAY,OAAO;QACvB,IAAI,eAAe,OAAO;QAE1B,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,SAAS;QAET,4CAA4C;QAC5C,IAAI,YAAY,KAAK,EAAE;YACrB,eAAe,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO;gBAAU,CAAC;QACvD;QAEA,4FAA4F;QAC5F,IAAI,OAAO;YACT,WAAW;gBACT,SAAS;YACX,GAAG;QACL;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,YAAY;QAEZ,4CAA4C;QAC5C,IAAI,YAAY,QAAQ,EAAE;YACxB,eAAe,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,UAAU;gBAAU,CAAC;QAC1D;QAEA,4FAA4F;QAC5F,IAAI,OAAO;YACT,WAAW;gBACT,SAAS;YACX,GAAG;QACL;IACF;IAGA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,0BAA0B;QAC1B,SAAS;QACT,kBAAkB;QAElB,kCAAkC;QAClC,MAAM,kBAAkB;QACxB,IAAI,iBAAiB;YACnB,SAAS;YACT,WAAW;YACX;QACF;QAEA,WAAW;QACX,IAAI;YACF,+DAA+D;YAC/D,mBAAmB;gBAAC;gBAAiC;aAAiB;YACtE,MAAM,WAAW,MAAM,MAAM,MAAM,IAAI,GAAG,WAAW,IAAI,UAAU;YACnE,IAAI,UAAU;gBACZ,2BAA2B;gBAC3B,MAAM,cAAc,SAAS,IAAI,CAAC,kBAAkB;gBACpD,IAAI,aAAa;oBACf,eAAe;oBACf,kBAAkB;oBAClB,mBAAmB;wBAAC;wBAAqB;wBAAgC;qBAAqC;oBAE9G,0CAA0C;oBAC1C,eAAe,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC,SAAS,IAAI;oBAErE,WAAW;wBACT,OAAO,OAAO,CAAC;oBACjB,GAAG;gBACL,OAAO;oBACL,kBAAkB;oBAClB,mBAAmB;wBAAC;wBAAqB;wBAA8B;qBAAiB;oBAExF,WAAW;wBACT,OAAO,OAAO,CAAC;oBACjB,GAAG;gBACL;YACF,OAAO;gBACL,SAAS;gBACT,WAAW;gBACX;YACF;QACF,EAAE,OAAO,KAAc;YACrB,6EAA6E;YAC7E,WAAW;YACX,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM,eAAe,CAAA,GAAA,uHAAA,CAAA,kBAAe,AAAD,EAAE;YACrC,SAAS;YACT,8DAA8D;YAC9D;QACF;IACF;IAEA,MAAM,4BAA4B,CAAC;QACjC,EAAE,cAAc;QAChB,yBAAyB;QACzB,kBAAkB;QAClB,WAAW;YACT,OAAO,IAAI,CAAC;QACd,GAAG;IACL;IAEA,+FAA+F;IAC/F,IAAI,CAAC,YAAY,WAAW,uBAAuB;QAEjD,qBACE,8OAAC,yLAAA,CAAA,iBAAc;YACb,WAAW;YACX,gBAAgB,kBAAkB,CAAC,CAAC,WAAW,eAAe,eAAe;YAC7E,mBAAkB;YAClB,iBAAiB,UAAU,kBAAkB;YAC7C,cAAc;sBAEd,cAAA,8OAAC;;;;;;;;;;IAGP;IAEA,qBACQ,8OAAC,iLAAA,CAAA,aAAU;QACT,OAAM;QACN,wBACE;;gBAAE;gBACoB;8BACpB,8OAAC;oBAAK,WAAU;8BAAiC;;;;;;;;QAKrD,kBAAkB;;YAEnB,uBACC,8OAAC,uLAAA,CAAA,gBAAa;gBACZ,MAAK;gBACL,SAAS;gBACT,WAAU;gBACV,aAAa;gBACb,WAAW,IAAM,SAAS;;;;;;YAI7B,gCACC,8OAAC,uLAAA,CAAA,gBAAa;gBACZ,MAAK;gBACL,SAAS;gBACT,WAAU;gBACV,aAAa;gBACb,WAAW,IAAM,kBAAkB;;;;;;0BAIvC,8OAAC;gBAAK,WAAU;gBAA6C,UAAU;;kCACrE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA6D;;;;;;0CAG9F,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,cAAa;oCACb,QAAQ;oCACR,OAAO;oCACP,UAAU;oCACV,WAAW,CAAC,qSAAqS,EAC/S,YAAY,KAAK,GACb,6FACA,gFACJ;oCACF,aAAY;;;;;;;;;;;4BAGf,YAAY,KAAK,kBAChB,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAe,SAAQ;kDACxD,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAoH,UAAS;;;;;;;;;;;oCAEzJ,YAAY,KAAK;;;;;;;;;;;;;kCAKxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA6D;;;;;;0CAGjG,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,cAAa;oCACb,QAAQ;oCACR,OAAO;oCACP,UAAU;oCACV,WAAW,CAAC,qSAAqS,EAC/S,YAAY,QAAQ,GAChB,6FACA,gFACJ;oCACF,aAAY;;;;;;;;;;;4BAGf,YAAY,QAAQ,kBACnB,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAe,SAAQ;kDACxD,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAoH,UAAS;;;;;;;;;;;oCAEzJ,YAAY,QAAQ;;;;;;;;;;;;;oBAM1B,CAAC,kCACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,OAAO;wCAC/C,WAAU;;;;;;kDAEZ,8OAAC;wCAAM,SAAQ;wCAAc,WAAU;kDAAsD;;;;;;;;;;;;0CAK/F,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAOP,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,wBACC;;kDACE,8OAAC;wCAAI,WAAU;;;;;;oCAAuE;;6DAIxF;;kDACE,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;oCACjE;;;;;;;;;;;;;;;;;;;;;;;;;AAS1B;AAEe,SAAS;IACtB,QAAQ,GAAG,CAAC;IACZ,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBACR,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;;;;;;;kBAGpC,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}
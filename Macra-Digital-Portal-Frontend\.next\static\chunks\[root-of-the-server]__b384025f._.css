/* [next]/internal/font/google/inter_6c5b4465.module.css [app-client] (css) */
@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/2a2d10660758e7fa-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/d6f0f7ef0a66b318-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/c0062fcfb5f4a9e6-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/1a97932d2ea76c90-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/e27fd546b8a0677f-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/a973f82a0d056f9e-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/06ba6ef833b337bc-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/2a2d10660758e7fa-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/d6f0f7ef0a66b318-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/c0062fcfb5f4a9e6-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/1a97932d2ea76c90-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/e27fd546b8a0677f-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/a973f82a0d056f9e-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/06ba6ef833b337bc-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/2a2d10660758e7fa-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/d6f0f7ef0a66b318-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/c0062fcfb5f4a9e6-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/1a97932d2ea76c90-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/e27fd546b8a0677f-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/a973f82a0d056f9e-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/06ba6ef833b337bc-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/2a2d10660758e7fa-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/d6f0f7ef0a66b318-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/c0062fcfb5f4a9e6-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/1a97932d2ea76c90-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/e27fd546b8a0677f-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/a973f82a0d056f9e-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/06ba6ef833b337bc-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/2a2d10660758e7fa-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/d6f0f7ef0a66b318-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/c0062fcfb5f4a9e6-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/1a97932d2ea76c90-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/e27fd546b8a0677f-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/a973f82a0d056f9e-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/06ba6ef833b337bc-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Fallback;
  src: local(Arial);
  ascent-override: 90.44%;
  descent-override: 22.52%;
  line-gap-override: 0.0%;
  size-adjust: 107.12%;
}

.inter_6c5b4465-module__mN240a__className {
  font-family: Inter, Inter Fallback;
  font-style: normal;
}

.inter_6c5b4465-module__mN240a__variable {
  --font-inter: "Inter", "Inter Fallback";
}

/* [project]/src/styles/toast.css [app-client] (css) */
.toast-container {
  z-index: 9999;
  pointer-events: none;
  flex-direction: column;
  gap: .5rem;
  display: flex;
  position: fixed;
  top: 1rem;
  right: 1rem;
}

.toast-wrapper {
  pointer-events: auto;
  pointer-events: auto;
  transition: transform .3s ease-in-out;
  position: relative;
}

.toast-wrapper[data-index="0"] {
  transform: translateY(0);
}

.toast-wrapper[data-index="1"] {
  transform: translateY(10px);
}

.toast-wrapper[data-index="2"] {
  transform: translateY(20px);
}

.toast-wrapper[data-index="3"] {
  transform: translateY(30px);
}

.toast-wrapper[data-index="4"] {
  transform: translateY(40px);
}

.toast-wrapper[data-index="5"] {
  transform: translateY(50px);
}

.toast-wrapper[data-index="6"] {
  transform: translateY(60px);
}

.toast-wrapper[data-index="7"] {
  transform: translateY(70px);
}

.toast-wrapper[data-index="8"] {
  transform: translateY(80px);
}

.toast-wrapper[data-index="9"] {
  transform: translateY(90px);
}

.toast-wrapper[data-index="10"] {
  transform: translateY(100px);
}

.toast-wrapper[data-index="11"] {
  transform: translateY(110px);
}

.toast-wrapper[data-index="12"] {
  transform: translateY(120px);
}

.toast-wrapper[data-index="13"] {
  transform: translateY(130px);
}

.toast-wrapper[data-index="14"] {
  transform: translateY(140px);
}

.toast-wrapper[data-index="15"] {
  transform: translateY(150px);
}

.toast-wrapper[data-index="16"] {
  transform: translateY(160px);
}

.toast-wrapper[data-index="17"] {
  transform: translateY(170px);
}

.toast-wrapper[data-index="18"] {
  transform: translateY(180px);
}

.toast-wrapper[data-index="19"] {
  transform: translateY(190px);
}

@media (max-width: 640px) {
  .toast-container {
    width: auto;
    top: 1rem;
    left: 1rem;
    right: 1rem;
  }
}

.toast-container {
  z-index: 9999 !important;
}

.toast-wrapper > div {
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, .1), 0 4px 6px rgba(0, 0, 0, .05);
}

/* [project]/src/styles/auth-animations.css [app-client] (css) */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(.95);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(.3);
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
  }

  70% {
    transform: scale(.9);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: .5;
  }
}

@keyframes fadeLoop {
  0%, 100% {
    opacity: .8;
  }

  50% {
    opacity: 1;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }

  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }

  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

@keyframes progressFill {
  from {
    width: 0%;
  }

  to {
    width: 100%;
  }
}

.animate-fadeIn {
  animation: .5s ease-out forwards fadeIn;
}

.animate-slideInFromTop {
  animation: .4s ease-out forwards slideInFromTop;
}

.animate-slideInFromBottom {
  animation: .4s ease-out forwards slideInFromBottom;
}

.animate-scaleIn {
  animation: .3s ease-out forwards scaleIn;
}

.animate-bounceIn {
  animation: .6s ease-out forwards bounceIn;
}

.animate-pulse {
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite pulse;
}

.animate-fadeLoop {
  animation: 3s ease-in-out infinite fadeLoop;
}

.animate-shake {
  animation: .5s ease-in-out shake;
}

.animate-progressFill {
  animation: .3s ease-out forwards progressFill;
}

.animate-delay-100 {
  animation-delay: .1s;
}

.animate-delay-200 {
  animation-delay: .2s;
}

.animate-delay-300 {
  animation-delay: .3s;
}

.animate-delay-500 {
  animation-delay: .5s;
}

.transition-smooth {
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
}

.transition-bounce {
  transition: all .3s cubic-bezier(.68, -.55, .265, 1.55);
}

.spinner-gradient {
  background: conic-gradient(rgba(0, 0, 0, 0), #dc2626);
  border-radius: 50%;
  animation: 1s linear infinite spin;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.field-focus-ring {
  transition: all .2s ease-in-out;
}

.field-focus-ring:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, .15);
}

.button-hover-lift {
  transition: all .2s ease-in-out;
}

.button-hover-lift:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, .15);
}

.status-message-enter {
  animation: .3s ease-out forwards slideInFromTop;
}

.status-message-exit {
  animation: .3s ease-out reverse forwards slideInFromTop;
}

.page-transition {
  animation: .4s ease-out forwards fadeIn;
}

.success-icon {
  animation: .6s ease-out forwards bounceIn;
}

.success-content {
  opacity: 0;
  animation: .5s ease-out .3s forwards fadeIn;
}

.loading-content {
  animation: .3s ease-out forwards fadeIn;
}

.auth-layout {
  animation: .4s ease-out forwards scaleIn;
}

.auth-header {
  animation: .5s ease-out forwards slideInFromTop;
}

.auth-form {
  opacity: 0;
  animation: .5s ease-out .1s forwards slideInFromBottom;
}

/* [project]/src/app/globals.css [app-client] (css) */
*, :before, :after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgba(59, 130, 246, .5);
  --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-shadow-colored: 0 0 rgba(0, 0, 0, 0);
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgba(59, 130, 246, .5);
  --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-shadow-colored: 0 0 rgba(0, 0, 0, 0);
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

*, :before, :after {
  box-sizing: border-box;
  border: 0 solid #e5e7eb;
}

:before, :after {
  --tw-content: "";
}

html, :host {
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;
  font-family: Inter, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif;
  line-height: 1.5;
}

body {
  line-height: inherit;
  margin: 0;
}

hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
}

a {
  color: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

b, strong {
  font-weight: bolder;
}

code, kbd, samp, pre {
  font-feature-settings: normal;
  font-variation-settings: normal;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
  font-size: 1em;
}

small {
  font-size: 80%;
}

sub, sup {
  vertical-align: baseline;
  font-size: 75%;
  line-height: 0;
  position: relative;
}

sub {
  bottom: -.25em;
}

sup {
  top: -.5em;
}

table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}

button, input, optgroup, select, textarea {
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

button, select {
  text-transform: none;
}

button {
  -webkit-appearance: button;
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
}

input:where([type="button"]) {
  -webkit-appearance: button;
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
}

input:where([type="reset"]) {
  -webkit-appearance: button;
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
}

input:where([type="submit"]) {
  -webkit-appearance: button;
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}

progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button {
  height: auto;
}

::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

summary {
  display: list-item;
}

blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol, ul, menu {
  margin: 0;
  padding: 0;
  list-style: none;
}

dialog {
  padding: 0;
}

textarea {
  resize: vertical;
}

input::-moz-placeholder {
  opacity: 1;
  color: #9ca3af;
}

textarea::-moz-placeholder {
  opacity: 1;
  color: #9ca3af;
}

input::placeholder, textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}

button, [role="button"] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}

img, svg, video, canvas, audio, iframe, embed, object {
  vertical-align: middle;
  display: block;
}

img, video {
  max-width: 100%;
  height: auto;
}

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

img {
  content-visibility: auto;
}

@media (prefers-reduced-motion: reduce) {
  *, :before, :after {
    transition-duration: .01ms !important;
    animation-duration: .01ms !important;
    animation-iteration-count: 1 !important;
  }
}

.gpu-accelerated {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  perspective: 1000px;
  transform: translateZ(0);
}

.container {
  width: 100%;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.btn-primary {
  will-change: background-color, transform;
  transition: all .2s ease-in-out;
}

.btn-primary:hover {
  transform: translateZ(0);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}

.custom-form-label {
  --tw-text-opacity: 1;
  color: rgba(55, 65, 81, var(--tw-text-opacity, 1));
  padding-bottom: .5rem;
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.25rem;
  display: block;
}

.enhanced-input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  --tw-border-opacity: 1;
  border-width: 1px;
  border-color: rgba(209, 213, 219, var(--tw-border-opacity, 1));
  border-radius: 12px;
  width: 100%;
  padding: .5rem .75rem;
  display: block;
}

.enhanced-input::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgba(156, 163, 175, var(--tw-placeholder-opacity, 1));
}

.enhanced-input {
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.enhanced-input:focus {
  --tw-border-opacity: 1;
  border-color: rgba(224, 43, 32, var(--tw-border-opacity, 1));
  outline-offset: 2px;
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(224, 43, 32, var(--tw-ring-opacity, 1));
  outline: 2px solid rgba(0, 0, 0, 0);
}

@media (min-width: 640px) {
  .enhanced-input {
    font-size: .875rem;
    line-height: 1.25rem;
  }
}

.enhanced-select {
  --tw-border-opacity: 1;
  border-width: 1px;
  border-color: rgba(209, 213, 219, var(--tw-border-opacity, 1));
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  width: 100%;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
  border-radius: 12px;
  padding: .5rem .75rem;
  display: block;
}

.enhanced-select:focus {
  --tw-border-opacity: 1;
  border-color: rgba(224, 43, 32, var(--tw-border-opacity, 1));
  outline-offset: 2px;
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(224, 43, 32, var(--tw-ring-opacity, 1));
  outline: 2px solid rgba(0, 0, 0, 0);
}

@media (min-width: 640px) {
  .enhanced-select {
    font-size: .875rem;
    line-height: 1.25rem;
  }
}

.enhanced-checkbox {
  --tw-border-opacity: 1;
  border-width: 1px;
  border-color: rgba(209, 213, 219, var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  width: 1.25rem;
  height: 1.25rem;
  color: rgba(224, 43, 32, var(--tw-text-opacity, 1));
  border-radius: 8px;
}

.enhanced-checkbox:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(224, 43, 32, var(--tw-ring-opacity, 1));
}

.enhanced-checkbox:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(75, 85, 99, var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgba(55, 65, 81, var(--tw-bg-opacity, 1));
}

.main-button {
  --tw-bg-opacity: 1;
  background-color: rgba(224, 43, 32, var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, var(--tw-text-opacity, 1));
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
  background-color: var(--primary);
  border-width: 1px;
  border-color: rgba(0, 0, 0, 0);
  border-radius: 12px;
  padding: .5rem 1rem;
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.25rem;
  display: inline-flex;
}

.main-button:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(185, 28, 28, var(--tw-bg-opacity, 1));
}

.main-button:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(254, 202, 202, var(--tw-ring-opacity, 1));
}

.secondary-main-button {
  --tw-border-opacity: 1;
  border-width: 1px;
  border-color: rgba(209, 213, 219, var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgba(55, 65, 81, var(--tw-text-opacity, 1));
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
  border-radius: 12px;
  padding: .5rem 1rem;
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.25rem;
  display: inline-flex;
}

.secondary-main-button:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(249, 250, 251, var(--tw-bg-opacity, 1));
}

.secondary-main-button:focus {
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(229, 231, 235, var(--tw-ring-opacity, 1));
  outline: 2px solid rgba(0, 0, 0, 0);
}

.custom-input {
  --tw-border-opacity: 1;
  border-width: 1px;
  border-color: rgba(209, 213, 219, var(--tw-border-opacity, 1));
  border-radius: 12px;
  width: 100%;
  margin-top: .25rem;
  padding: .5rem .75rem;
  font-size: .875rem;
  line-height: 1.25rem;
  display: block;
}

.custom-input::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgba(156, 163, 175, var(--tw-placeholder-opacity, 1));
}

.custom-input:focus {
  --tw-border-opacity: 1;
  border-color: rgba(239, 68, 68, var(--tw-border-opacity, 1));
  --tw-shadow: var(--tw-shadow-colored);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
  --tw-shadow-color: #fecaca;
  outline-offset: 2px;
  outline: 2px solid rgba(0, 0, 0, 0);
}

.form-section {
  flex-direction: column;
  row-gap: .5rem;
  display: flex;
}

.form-section > :not([hidden]) ~ :not([hidden]) {
  border-style: solid;
}

.form-section {
  --tw-border-opacity: 1;
  border-color: rgba(209, 213, 219, var(--tw-border-opacity, 1));
}

@media (min-width: 1024px) {
  .form-section {
    border-bottom-width: 1px;
  }
}

.inner-form-section {
  -moz-column-gap: 1.5rem;
  gap: 1.5rem;
  margin-top: 1rem;
  display: grid;
}

@media (min-width: 640px) {
  .inner-form-section {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .inner-form-section {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.tab-heading {
  flex-direction: column;
  margin-bottom: 1.5rem;
  display: flex;
}

.tab-heading > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

@media (min-width: 640px) {
  .tab-heading {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .tab-heading > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
}

.form-group {
  margin-bottom: 1.5rem;
}

.sr-only {
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

.pointer-events-none {
  pointer-events: none;
}

.visible {
  visibility: visible;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: -webkit-sticky;
  position: sticky;
}

.-inset-2 {
  top: -.5rem;
  bottom: -.5rem;
  left: -.5rem;
  right: -.5rem;
}

.inset-0 {
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.inset-y-0 {
  top: 0;
  bottom: 0;
}

.-bottom-1 {
  bottom: -.25rem;
}

.-right-1 {
  right: -.25rem;
}

.-right-2 {
  right: -.5rem;
}

.-right-3 {
  right: -.75rem;
}

.-top-1 {
  top: -.25rem;
}

.-top-2 {
  top: -.5rem;
}

.bottom-0 {
  bottom: 0;
}

.bottom-4 {
  bottom: 1rem;
}

.bottom-6 {
  bottom: 1.5rem;
}

.bottom-full {
  bottom: 100%;
}

.left-0 {
  left: 0;
}

.left-1\/2 {
  left: 50%;
}

.left-3 {
  left: .75rem;
}

.left-4 {
  left: 1rem;
}

.left-6 {
  left: 1.5rem;
}

.left-full {
  left: 100%;
}

.right-0 {
  right: 0;
}

.right-2 {
  right: .5rem;
}

.right-3 {
  right: .75rem;
}

.right-4 {
  right: 1rem;
}

.right-6 {
  right: 1.5rem;
}

.right-full {
  right: 100%;
}

.top-0 {
  top: 0;
}

.top-1\/2 {
  top: 50%;
}

.top-10 {
  top: 2.5rem;
}

.top-2 {
  top: .5rem;
}

.top-20 {
  top: 5rem;
}

.top-3 {
  top: .75rem;
}

.top-4 {
  top: 1rem;
}

.top-6 {
  top: 1.5rem;
}

.top-7 {
  top: 1.75rem;
}

.top-8 {
  top: 2rem;
}

.top-full {
  top: 100%;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[9999\] {
  z-index: 9999;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-5 {
  grid-column: span 5 / span 5;
}

.m-0 {
  margin: 0;
}

.m-2 {
  margin: .5rem;
}

.m-auto {
  margin: auto;
}

.-mx-1\.5 {
  margin-left: -.375rem;
  margin-right: -.375rem;
}

.-my-1\.5 {
  margin-top: -.375rem;
  margin-bottom: -.375rem;
}

.-my-2 {
  margin-top: -.5rem;
  margin-bottom: -.5rem;
}

.mx-2 {
  margin-left: .5rem;
  margin-right: .5rem;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.mx-6 {
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-2 {
  margin-top: .5rem;
  margin-bottom: .5rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.-mb-px {
  margin-bottom: -1px;
}

.-ml-1 {
  margin-left: -.25rem;
}

.-mr-1 {
  margin-right: -.25rem;
}

.-mr-12 {
  margin-right: -3rem;
}

.-mt-1 {
  margin-top: -.25rem;
}

.-mt-2 {
  margin-top: -.5rem;
}

.mb-1 {
  margin-bottom: .25rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mb-2 {
  margin-bottom: .5rem;
}

.mb-3 {
  margin-bottom: .75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.ml-1 {
  margin-left: .25rem;
}

.ml-2 {
  margin-left: .5rem;
}

.ml-3 {
  margin-left: .75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-5 {
  margin-left: 1.25rem;
}

.ml-6 {
  margin-left: 1.5rem;
}

.ml-8 {
  margin-left: 2rem;
}

.ml-auto {
  margin-left: auto;
}

.mr-1 {
  margin-right: .25rem;
}

.mr-1\.5 {
  margin-right: .375rem;
}

.mr-2 {
  margin-right: .5rem;
}

.mr-3 {
  margin-right: .75rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mt-0\.5 {
  margin-top: .125rem;
}

.mt-1 {
  margin-top: .25rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-2 {
  margin-top: .5rem;
}

.mt-3 {
  margin-top: .75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.line-clamp-2 {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.line-clamp-3 {
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.flow-root {
  display: flow-root;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.hidden {
  display: none;
}

.size-6 {
  width: 1.5rem;
  height: 1.5rem;
}

.h-0\.5 {
  height: .125rem;
}

.h-1 {
  height: .25rem;
}

.h-1\.5 {
  height: .375rem;
}

.h-10 {
  height: 2.5rem;
}

.h-11 {
  height: 2.75rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-16 {
  height: 4rem;
}

.h-2 {
  height: .5rem;
}

.h-20 {
  height: 5rem;
}

.h-24 {
  height: 6rem;
}

.h-28 {
  height: 7rem;
}

.h-3 {
  height: .75rem;
}

.h-32 {
  height: 8rem;
}

.h-4 {
  height: 1rem;
}

.h-40 {
  height: 10rem;
}

.h-48 {
  height: 12rem;
}

.h-5 {
  height: 1.25rem;
}

.h-56 {
  height: 14rem;
}

.h-6 {
  height: 1.5rem;
}

.h-64 {
  height: 16rem;
}

.h-8 {
  height: 2rem;
}

.h-9 {
  height: 2.25rem;
}

.h-96 {
  height: 24rem;
}

.h-\[1px\] {
  height: 1px;
}

.h-auto {
  height: auto;
}

.h-full {
  height: 100%;
}

.h-screen {
  height: 100vh;
}

.max-h-0 {
  max-height: 0;
}

.max-h-12 {
  max-height: 3rem;
}

.max-h-16 {
  max-height: 4rem;
}

.max-h-32 {
  max-height: 8rem;
}

.max-h-40 {
  max-height: 10rem;
}

.max-h-60 {
  max-height: 15rem;
}

.max-h-64 {
  max-height: 16rem;
}

.max-h-96 {
  max-height: 24rem;
}

.max-h-\[600px\] {
  max-height: 600px;
}

.max-h-\[90vh\] {
  max-height: 90vh;
}

.max-h-\[calc\(100vh-200px\)\] {
  max-height: calc(100vh - 200px);
}

.max-h-\[calc\(90vh-120px\)\] {
  max-height: calc(90vh - 120px);
}

.max-h-full {
  max-height: 100%;
}

.max-h-none {
  max-height: none;
}

.max-h-screen {
  max-height: 100vh;
}

.min-h-0 {
  min-height: 0;
}

.min-h-40 {
  min-height: 10rem;
}

.min-h-64 {
  min-height: 16rem;
}

.min-h-96 {
  min-height: 24rem;
}

.min-h-\[120px\] {
  min-height: 120px;
}

.min-h-\[160px\] {
  min-height: 160px;
}

.min-h-\[180px\] {
  min-height: 180px;
}

.min-h-\[400px\] {
  min-height: 400px;
}

.min-h-\[80px\] {
  min-height: 80px;
}

.min-h-\[calc\(100vh-80px\)\] {
  min-height: calc(100vh - 80px);
}

.min-h-full {
  min-height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

.w-0 {
  width: 0;
}

.w-1\/2 {
  width: 50%;
}

.w-1\/3 {
  width: 33.3333%;
}

.w-1\/5 {
  width: 20%;
}

.w-10 {
  width: 2.5rem;
}

.w-11 {
  width: 2.75rem;
}

.w-11\/12 {
  width: 91.6667%;
}

.w-12 {
  width: 3rem;
}

.w-14 {
  width: 3.5rem;
}

.w-16 {
  width: 4rem;
}

.w-2 {
  width: .5rem;
}

.w-2\/3 {
  width: 66.6667%;
}

.w-2\/5 {
  width: 40%;
}

.w-20 {
  width: 5rem;
}

.w-24 {
  width: 6rem;
}

.w-28 {
  width: 7rem;
}

.w-3 {
  width: .75rem;
}

.w-3\/4 {
  width: 75%;
}

.w-3\/5 {
  width: 60%;
}

.w-32 {
  width: 8rem;
}

.w-4 {
  width: 1rem;
}

.w-4\/5 {
  width: 80%;
}

.w-4\/6 {
  width: 66.6667%;
}

.w-40 {
  width: 10rem;
}

.w-48 {
  width: 12rem;
}

.w-5 {
  width: 1.25rem;
}

.w-5\/6 {
  width: 83.3333%;
}

.w-6 {
  width: 1.5rem;
}

.w-64 {
  width: 16rem;
}

.w-7 {
  width: 1.75rem;
}

.w-72 {
  width: 18rem;
}

.w-8 {
  width: 2rem;
}

.w-80 {
  width: 20rem;
}

.w-96 {
  width: 24rem;
}

.w-\[1px\] {
  width: 1px;
}

.w-\[85\%\] {
  width: 85%;
}

.w-auto {
  width: auto;
}

.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}

.w-full {
  width: 100%;
}

.min-w-0 {
  min-width: 0;
}

.min-w-\[1\.25rem\] {
  min-width: 1.25rem;
}

.min-w-\[120px\] {
  min-width: 120px;
}

.min-w-full {
  min-width: 100%;
}

.max-w-20 {
  max-width: 5rem;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-5xl {
  max-width: 64rem;
}

.max-w-60 {
  max-width: 15rem;
}

.max-w-6xl {
  max-width: 72rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.max-w-\[120px\] {
  max-width: 120px;
}

.max-w-full {
  max-width: 100%;
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-none {
  max-width: none;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-xl {
  max-width: 36rem;
}

.max-w-xs {
  max-width: 20rem;
}

.flex-1 {
  flex: 1;
}

.flex-auto {
  flex: auto;
}

.flex-shrink {
  flex-shrink: 1;
}

.flex-shrink-0, .shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

.caption-bottom {
  caption-side: bottom;
}

.border-collapse {
  border-collapse: collapse;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-2 {
  --tw-translate-y: -.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-1 {
  --tw-translate-x: .25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-6 {
  --tw-translate-x: 1.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-2 {
  --tw-translate-y: .5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-90 {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes bounce {
  0%, 100% {
    animation-timing-function: cubic-bezier(.8, 0, 1, 1);
    transform: translateY(-25%);
  }

  50% {
    animation-timing-function: cubic-bezier(0, 0, .2, 1);
    transform: none;
  }
}

.animate-bounce {
  animation: 1s infinite bounce;
}

@keyframes fadeLoop {
  0%, 100% {
    opacity: .1;
  }

  50% {
    opacity: 1;
  }
}

.animate-fadeLoop {
  animation: 2s ease-in-out infinite fadeLoop;
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite pulse;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: 1s linear infinite spin;
}

.cursor-default {
  cursor: default;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.resize-none {
  resize: none;
}

.resize-y {
  resize: vertical;
}

.resize {
  resize: both;
}

.list-inside {
  list-style-position: inside;
}

.list-decimal {
  list-style-type: decimal;
}

.list-disc {
  list-style-type: disc;
}

.appearance-none {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}

.grid-rows-2 {
  grid-template-rows: repeat(2, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.place-content-center {
  place-content: center;
}

.place-content-start {
  place-content: start;
}

.place-content-end {
  place-content: end;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.gap-0 {
  gap: 0;
}

.gap-1 {
  gap: .25rem;
}

.gap-2 {
  gap: .5rem;
}

.gap-2\.5 {
  gap: .625rem;
}

.gap-3 {
  gap: .75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-x-4 {
  -moz-column-gap: 1rem;
  column-gap: 1rem;
}

.gap-x-6 {
  -moz-column-gap: 1.5rem;
  column-gap: 1.5rem;
}

.gap-y-2 {
  row-gap: .5rem;
}

.gap-y-4 {
  row-gap: 1rem;
}

.gap-y-6 {
  row-gap: 1.5rem;
}

.-space-x-px > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-1px * var(--tw-space-x-reverse));
  margin-left: calc(-1px * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(.25rem * var(--tw-space-x-reverse));
  margin-left: calc(.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(.5rem * var(--tw-space-x-reverse));
  margin-left: calc(.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(.75rem * var(--tw-space-x-reverse));
  margin-left: calc(.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.25rem * var(--tw-space-x-reverse));
  margin-left: calc(1.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.25rem * var(--tw-space-y-reverse));
}

.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.375rem * var(--tw-space-y-reverse));
}

.space-y-10 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));
}

.space-y-12 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-solid > :not([hidden]) ~ :not([hidden]) {
  border-style: solid;
}

.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgba(229, 231, 235, var(--tw-divide-opacity, 1));
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-visible {
  overflow: visible;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.truncate {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.text-ellipsis {
  text-overflow: ellipsis;
}

.hyphens-auto {
  -webkit-hyphens: auto;
  hyphens: auto;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.break-words {
  overflow-wrap: break-word;
}

.break-all {
  word-break: break-all;
}

.rounded {
  border-radius: 8px;
}

.rounded-2xl {
  border-radius: 24px;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 16px;
}

.rounded-md {
  border-radius: 12px;
}

.rounded-none {
  border-radius: 0;
}

.rounded-sm {
  border-radius: 4px;
}

.rounded-xl {
  border-radius: 20px;
}

.rounded-b-lg {
  border-bottom-right-radius: 16px;
  border-bottom-left-radius: 16px;
}

.rounded-l-md {
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
}

.rounded-r-lg {
  border-top-right-radius: 16px;
  border-bottom-right-radius: 16px;
}

.rounded-r-md {
  border-top-right-radius: 12px;
  border-bottom-right-radius: 12px;
}

.rounded-t-lg {
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0;
}

.border-2 {
  border-width: 2px;
}

.border-4 {
  border-width: 4px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-b-4 {
  border-bottom-width: 4px;
}

.border-l {
  border-left-width: 1px;
}

.border-l-4 {
  border-left-width: 4px;
}

.border-r-2 {
  border-right-width: 2px;
}

.border-r-4 {
  border-right-width: 4px;
}

.border-t {
  border-top-width: 1px;
}

.border-t-2 {
  border-top-width: 2px;
}

.border-t-4 {
  border-top-width: 4px;
}

.border-dashed {
  border-style: dashed;
}

.border-none {
  border-style: none;
}

.border-blue-100 {
  --tw-border-opacity: 1;
  border-color: rgba(219, 234, 254, var(--tw-border-opacity, 1));
}

.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgba(191, 219, 254, var(--tw-border-opacity, 1));
}

.border-blue-300 {
  --tw-border-opacity: 1;
  border-color: rgba(147, 197, 253, var(--tw-border-opacity, 1));
}

.border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgba(96, 165, 250, var(--tw-border-opacity, 1));
}

.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgba(59, 130, 246, var(--tw-border-opacity, 1));
}

.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgba(37, 99, 235, var(--tw-border-opacity, 1));
}

.border-current {
  border-color: currentColor;
}

.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgba(243, 244, 246, var(--tw-border-opacity, 1));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgba(229, 231, 235, var(--tw-border-opacity, 1));
}

.border-gray-200\/50 {
  border-color: rgba(229, 231, 235, .5);
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgba(209, 213, 219, var(--tw-border-opacity, 1));
}

.border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgba(156, 163, 175, var(--tw-border-opacity, 1));
}

.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgba(55, 65, 81, var(--tw-border-opacity, 1));
}

.border-gray-800 {
  --tw-border-opacity: 1;
  border-color: rgba(31, 41, 55, var(--tw-border-opacity, 1));
}

.border-green-100 {
  --tw-border-opacity: 1;
  border-color: rgba(220, 252, 231, var(--tw-border-opacity, 1));
}

.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgba(187, 247, 208, var(--tw-border-opacity, 1));
}

.border-green-300 {
  --tw-border-opacity: 1;
  border-color: rgba(134, 239, 172, var(--tw-border-opacity, 1));
}

.border-green-400 {
  --tw-border-opacity: 1;
  border-color: rgba(74, 222, 128, var(--tw-border-opacity, 1));
}

.border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgba(34, 197, 94, var(--tw-border-opacity, 1));
}

.border-green-600 {
  --tw-border-opacity: 1;
  border-color: rgba(22, 163, 74, var(--tw-border-opacity, 1));
}

.border-indigo-200 {
  --tw-border-opacity: 1;
  border-color: rgba(199, 210, 254, var(--tw-border-opacity, 1));
}

.border-orange-200 {
  --tw-border-opacity: 1;
  border-color: rgba(254, 215, 170, var(--tw-border-opacity, 1));
}

.border-primary {
  --tw-border-opacity: 1;
  border-color: rgba(224, 43, 32, var(--tw-border-opacity, 1));
}

.border-primary\/20 {
  border-color: rgba(224, 43, 32, .2);
}

.border-purple-200 {
  --tw-border-opacity: 1;
  border-color: rgba(233, 213, 255, var(--tw-border-opacity, 1));
}

.border-red-100 {
  --tw-border-opacity: 1;
  border-color: rgba(254, 226, 226, var(--tw-border-opacity, 1));
}

.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgba(254, 202, 202, var(--tw-border-opacity, 1));
}

.border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgba(252, 165, 165, var(--tw-border-opacity, 1));
}

.border-red-400 {
  --tw-border-opacity: 1;
  border-color: rgba(248, 113, 113, var(--tw-border-opacity, 1));
}

.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgba(239, 68, 68, var(--tw-border-opacity, 1));
}

.border-red-600 {
  --tw-border-opacity: 1;
  border-color: rgba(220, 38, 38, var(--tw-border-opacity, 1));
}

.border-teal-200 {
  --tw-border-opacity: 1;
  border-color: rgba(153, 246, 228, var(--tw-border-opacity, 1));
}

.border-transparent {
  border-color: rgba(0, 0, 0, 0);
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgba(255, 255, 255, var(--tw-border-opacity, 1));
}

.border-yellow-100 {
  --tw-border-opacity: 1;
  border-color: rgba(254, 249, 195, var(--tw-border-opacity, 1));
}

.border-yellow-200 {
  --tw-border-opacity: 1;
  border-color: rgba(254, 240, 138, var(--tw-border-opacity, 1));
}

.border-yellow-300 {
  --tw-border-opacity: 1;
  border-color: rgba(253, 224, 71, var(--tw-border-opacity, 1));
}

.border-yellow-600 {
  --tw-border-opacity: 1;
  border-color: rgba(202, 138, 4, var(--tw-border-opacity, 1));
}

.border-t-transparent {
  border-top-color: rgba(0, 0, 0, 0);
}

.bg-amber-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(254, 243, 199, var(--tw-bg-opacity, 1));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgba(0, 0, 0, var(--tw-bg-opacity, 1));
}

.bg-black\/50 {
  background-color: rgba(0, 0, 0, .5);
}

.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(219, 234, 254, var(--tw-bg-opacity, 1));
}

.bg-blue-200 {
  --tw-bg-opacity: 1;
  background-color: rgba(191, 219, 254, var(--tw-bg-opacity, 1));
}

.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(239, 246, 255, var(--tw-bg-opacity, 1));
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(59, 130, 246, var(--tw-bg-opacity, 1));
}

.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgba(37, 99, 235, var(--tw-bg-opacity, 1));
}

.bg-emerald-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(209, 250, 229, var(--tw-bg-opacity, 1));
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(243, 244, 246, var(--tw-bg-opacity, 1));
}

.bg-gray-100\/50 {
  background-color: rgba(243, 244, 246, .5);
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgba(229, 231, 235, var(--tw-bg-opacity, 1));
}

.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgba(209, 213, 219, var(--tw-bg-opacity, 1));
}

.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgba(156, 163, 175, var(--tw-bg-opacity, 1));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(249, 250, 251, var(--tw-bg-opacity, 1));
}

.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(107, 114, 128, var(--tw-bg-opacity, 1));
}

.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgba(75, 85, 99, var(--tw-bg-opacity, 1));
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgba(31, 41, 55, var(--tw-bg-opacity, 1));
}

.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgba(17, 24, 39, var(--tw-bg-opacity, 1));
}

.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(220, 252, 231, var(--tw-bg-opacity, 1));
}

.bg-green-200 {
  --tw-bg-opacity: 1;
  background-color: rgba(187, 247, 208, var(--tw-bg-opacity, 1));
}

.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(240, 253, 244, var(--tw-bg-opacity, 1));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(34, 197, 94, var(--tw-bg-opacity, 1));
}

.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgba(22, 163, 74, var(--tw-bg-opacity, 1));
}

.bg-green-800 {
  --tw-bg-opacity: 1;
  background-color: rgba(22, 101, 52, var(--tw-bg-opacity, 1));
}

.bg-indigo-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(224, 231, 255, var(--tw-bg-opacity, 1));
}

.bg-indigo-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(238, 242, 255, var(--tw-bg-opacity, 1));
}

.bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgba(79, 70, 229, var(--tw-bg-opacity, 1));
}

.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(255, 237, 213, var(--tw-bg-opacity, 1));
}

.bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(255, 247, 237, var(--tw-bg-opacity, 1));
}

.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(249, 115, 22, var(--tw-bg-opacity, 1));
}

.bg-orange-600 {
  --tw-bg-opacity: 1;
  background-color: rgba(234, 88, 12, var(--tw-bg-opacity, 1));
}

.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgba(224, 43, 32, var(--tw-bg-opacity, 1));
}

.bg-primary\/10 {
  background-color: rgba(224, 43, 32, .1);
}

.bg-primary\/5 {
  background-color: rgba(224, 43, 32, .05);
}

.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(243, 232, 255, var(--tw-bg-opacity, 1));
}

.bg-purple-200 {
  --tw-bg-opacity: 1;
  background-color: rgba(233, 213, 255, var(--tw-bg-opacity, 1));
}

.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(250, 245, 255, var(--tw-bg-opacity, 1));
}

.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(168, 85, 247, var(--tw-bg-opacity, 1));
}

.bg-purple-600 {
  --tw-bg-opacity: 1;
  background-color: rgba(147, 51, 234, var(--tw-bg-opacity, 1));
}

.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(254, 226, 226, var(--tw-bg-opacity, 1));
}

.bg-red-200 {
  --tw-bg-opacity: 1;
  background-color: rgba(254, 202, 202, var(--tw-bg-opacity, 1));
}

.bg-red-400 {
  --tw-bg-opacity: 1;
  background-color: rgba(248, 113, 113, var(--tw-bg-opacity, 1));
}

.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(254, 242, 242, var(--tw-bg-opacity, 1));
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(239, 68, 68, var(--tw-bg-opacity, 1));
}

.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgba(220, 38, 38, var(--tw-bg-opacity, 1));
}

.bg-secondary {
  --tw-bg-opacity: 1;
  background-color: rgba(99, 102, 241, var(--tw-bg-opacity, 1));
}

.bg-teal-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(204, 251, 241, var(--tw-bg-opacity, 1));
}

.bg-teal-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(240, 253, 250, var(--tw-bg-opacity, 1));
}

.bg-teal-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(20, 184, 166, var(--tw-bg-opacity, 1));
}

.bg-transparent {
  background-color: rgba(0, 0, 0, 0);
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity, 1));
}

.bg-white\/80 {
  background-color: rgba(255, 255, 255, .8);
}

.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(254, 249, 195, var(--tw-bg-opacity, 1));
}

.bg-yellow-200 {
  --tw-bg-opacity: 1;
  background-color: rgba(254, 240, 138, var(--tw-bg-opacity, 1));
}

.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(254, 252, 232, var(--tw-bg-opacity, 1));
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(234, 179, 8, var(--tw-bg-opacity, 1));
}

.bg-yellow-600 {
  --tw-bg-opacity: 1;
  background-color: rgba(202, 138, 4, var(--tw-bg-opacity, 1));
}

.bg-opacity-10 {
  --tw-bg-opacity: .1;
}

.bg-opacity-20 {
  --tw-bg-opacity: .2;
}

.bg-opacity-40 {
  --tw-bg-opacity: .4;
}

.bg-opacity-50 {
  --tw-bg-opacity: .5;
}

.bg-opacity-75 {
  --tw-bg-opacity: .75;
}

.bg-opacity-80 {
  --tw-bg-opacity: .8;
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-blue-50 {
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(239, 246, 255, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-50 {
  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(249, 250, 251, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-50 {
  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(240, 253, 244, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-500 {
  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(34, 197, 94, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-indigo-500 {
  --tw-gradient-from: #6366f1 var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(99, 102, 241, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary {
  --tw-gradient-from: #e02b20 var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(224, 43, 32, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary\/5 {
  --tw-gradient-from: rgba(224, 43, 32, .05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(224, 43, 32, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-50 {
  --tw-gradient-from: #faf5ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(250, 245, 255, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-red-100 {
  --tw-gradient-from: #fee2e2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(254, 226, 226, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-red-50 {
  --tw-gradient-from: #fef2f2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(254, 242, 242, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-red-500 {
  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(239, 68, 68, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-red-600 {
  --tw-gradient-from: #dc2626 var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(220, 38, 38, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-red-500 {
  --tw-gradient-to: rgba(239, 68, 68, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #ef4444 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-red-700 {
  --tw-gradient-to: rgba(185, 28, 28, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #b91c1c var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-white {
  --tw-gradient-to: rgba(255, 255, 255, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.to-blue-100 {
  --tw-gradient-to: #dbeafe var(--tw-gradient-to-position);
}

.to-blue-50 {
  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);
}

.to-blue-600 {
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}

.to-emerald-50 {
  --tw-gradient-to: #ecfdf5 var(--tw-gradient-to-position);
}

.to-green-100 {
  --tw-gradient-to: #dcfce7 var(--tw-gradient-to-position);
}

.to-indigo-50 {
  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);
}

.to-orange-50 {
  --tw-gradient-to: #fff7ed var(--tw-gradient-to-position);
}

.to-orange-600 {
  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);
}

.to-primary {
  --tw-gradient-to: #e02b20 var(--tw-gradient-to-position);
}

.to-primary\/10 {
  --tw-gradient-to: rgba(224, 43, 32, .1) var(--tw-gradient-to-position);
}

.to-purple-100 {
  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);
}

.to-purple-600 {
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}

.to-red-100 {
  --tw-gradient-to: #fee2e2 var(--tw-gradient-to-position);
}

.to-red-600 {
  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);
}

.to-red-700 {
  --tw-gradient-to: #b91c1c var(--tw-gradient-to-position);
}

.to-red-800 {
  --tw-gradient-to: #991b1b var(--tw-gradient-to-position);
}

.to-rose-100 {
  --tw-gradient-to: #ffe4e6 var(--tw-gradient-to-position);
}

.to-rose-50 {
  --tw-gradient-to: #fff1f2 var(--tw-gradient-to-position);
}

.bg-\[length\:16px_16px\] {
  background-size: 16px 16px;
}

.bg-right {
  background-position: 100%;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.object-contain {
  -o-object-fit: contain;
  object-fit: contain;
}

.object-cover {
  -o-object-fit: cover;
  object-fit: cover;
}

.p-0 {
  padding: 0;
}

.p-1 {
  padding: .25rem;
}

.p-1\.5 {
  padding: .375rem;
}

.p-2 {
  padding: .5rem;
}

.p-3 {
  padding: .75rem;
}

.p-4 {
  padding: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.px-1 {
  padding-left: .25rem;
  padding-right: .25rem;
}

.px-1\.5 {
  padding-left: .375rem;
  padding-right: .375rem;
}

.px-2 {
  padding-left: .5rem;
  padding-right: .5rem;
}

.px-2\.5 {
  padding-left: .625rem;
  padding-right: .625rem;
}

.px-3 {
  padding-left: .75rem;
  padding-right: .75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-0\.5 {
  padding-top: .125rem;
  padding-bottom: .125rem;
}

.py-1 {
  padding-top: .25rem;
  padding-bottom: .25rem;
}

.py-1\.5 {
  padding-top: .375rem;
  padding-bottom: .375rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-2 {
  padding-top: .5rem;
  padding-bottom: .5rem;
}

.py-2\.5 {
  padding-top: .625rem;
  padding-bottom: .625rem;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.py-3 {
  padding-top: .75rem;
  padding-bottom: .75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.pb-2 {
  padding-bottom: .5rem;
}

.pb-20 {
  padding-bottom: 5rem;
}

.pb-3 {
  padding-bottom: .75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pl-1 {
  padding-left: .25rem;
}

.pl-10 {
  padding-left: 2.5rem;
}

.pl-3 {
  padding-left: .75rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-5 {
  padding-left: 1.25rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pl-8 {
  padding-left: 2rem;
}

.pr-10 {
  padding-right: 2.5rem;
}

.pr-2 {
  padding-right: .5rem;
}

.pr-3 {
  padding-right: .75rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pt-0 {
  padding-top: 0;
}

.pt-2 {
  padding-top: .5rem;
}

.pt-3 {
  padding-top: .75rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-8 {
  padding-top: 2rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.align-middle {
  vertical-align: middle;
}

.align-bottom {
  vertical-align: bottom;
}

.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
}

.font-sans {
  font-family: Inter, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: .875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: .75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.italic {
  font-style: italic;
}

.leading-4 {
  line-height: 1rem;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-7 {
  line-height: 1.75rem;
}

.leading-none {
  line-height: 1;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-snug {
  line-height: 1.375;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-tight {
  letter-spacing: -.025em;
}

.tracking-wide {
  letter-spacing: .025em;
}

.tracking-wider {
  letter-spacing: .05em;
}

.tracking-widest {
  letter-spacing: .1em;
}

.text-amber-600 {
  --tw-text-opacity: 1;
  color: rgba(217, 119, 6, var(--tw-text-opacity, 1));
}

.text-amber-800 {
  --tw-text-opacity: 1;
  color: rgba(146, 64, 14, var(--tw-text-opacity, 1));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgba(0, 0, 0, var(--tw-text-opacity, 1));
}

.text-blue-100 {
  --tw-text-opacity: 1;
  color: rgba(219, 234, 254, var(--tw-text-opacity, 1));
}

.text-blue-200 {
  --tw-text-opacity: 1;
  color: rgba(191, 219, 254, var(--tw-text-opacity, 1));
}

.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgba(96, 165, 250, var(--tw-text-opacity, 1));
}

.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgba(59, 130, 246, var(--tw-text-opacity, 1));
}

.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgba(37, 99, 235, var(--tw-text-opacity, 1));
}

.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgba(29, 78, 216, var(--tw-text-opacity, 1));
}

.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgba(30, 64, 175, var(--tw-text-opacity, 1));
}

.text-blue-900 {
  --tw-text-opacity: 1;
  color: rgba(30, 58, 138, var(--tw-text-opacity, 1));
}

.text-emerald-600 {
  --tw-text-opacity: 1;
  color: rgba(5, 150, 105, var(--tw-text-opacity, 1));
}

.text-emerald-800 {
  --tw-text-opacity: 1;
  color: rgba(6, 95, 70, var(--tw-text-opacity, 1));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgba(209, 213, 219, var(--tw-text-opacity, 1));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgba(156, 163, 175, var(--tw-text-opacity, 1));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgba(107, 114, 128, var(--tw-text-opacity, 1));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgba(75, 85, 99, var(--tw-text-opacity, 1));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgba(55, 65, 81, var(--tw-text-opacity, 1));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgba(31, 41, 55, var(--tw-text-opacity, 1));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgba(17, 24, 39, var(--tw-text-opacity, 1));
}

.text-green-400 {
  --tw-text-opacity: 1;
  color: rgba(74, 222, 128, var(--tw-text-opacity, 1));
}

.text-green-500 {
  --tw-text-opacity: 1;
  color: rgba(34, 197, 94, var(--tw-text-opacity, 1));
}

.text-green-600 {
  --tw-text-opacity: 1;
  color: rgba(22, 163, 74, var(--tw-text-opacity, 1));
}

.text-green-700 {
  --tw-text-opacity: 1;
  color: rgba(21, 128, 61, var(--tw-text-opacity, 1));
}

.text-green-800 {
  --tw-text-opacity: 1;
  color: rgba(22, 101, 52, var(--tw-text-opacity, 1));
}

.text-green-900 {
  --tw-text-opacity: 1;
  color: rgba(20, 83, 45, var(--tw-text-opacity, 1));
}

.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgba(79, 70, 229, var(--tw-text-opacity, 1));
}

.text-indigo-800 {
  --tw-text-opacity: 1;
  color: rgba(55, 48, 163, var(--tw-text-opacity, 1));
}

.text-orange-500 {
  --tw-text-opacity: 1;
  color: rgba(249, 115, 22, var(--tw-text-opacity, 1));
}

.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgba(234, 88, 12, var(--tw-text-opacity, 1));
}

.text-orange-800 {
  --tw-text-opacity: 1;
  color: rgba(154, 52, 18, var(--tw-text-opacity, 1));
}

.text-primary {
  --tw-text-opacity: 1;
  color: rgba(224, 43, 32, var(--tw-text-opacity, 1));
}

.text-purple-500 {
  --tw-text-opacity: 1;
  color: rgba(168, 85, 247, var(--tw-text-opacity, 1));
}

.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgba(147, 51, 234, var(--tw-text-opacity, 1));
}

.text-purple-700 {
  --tw-text-opacity: 1;
  color: rgba(126, 34, 206, var(--tw-text-opacity, 1));
}

.text-purple-800 {
  --tw-text-opacity: 1;
  color: rgba(107, 33, 168, var(--tw-text-opacity, 1));
}

.text-red-100 {
  --tw-text-opacity: 1;
  color: rgba(254, 226, 226, var(--tw-text-opacity, 1));
}

.text-red-200 {
  --tw-text-opacity: 1;
  color: rgba(254, 202, 202, var(--tw-text-opacity, 1));
}

.text-red-400 {
  --tw-text-opacity: 1;
  color: rgba(248, 113, 113, var(--tw-text-opacity, 1));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgba(239, 68, 68, var(--tw-text-opacity, 1));
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgba(220, 38, 38, var(--tw-text-opacity, 1));
}

.text-red-700 {
  --tw-text-opacity: 1;
  color: rgba(185, 28, 28, var(--tw-text-opacity, 1));
}

.text-red-800 {
  --tw-text-opacity: 1;
  color: rgba(153, 27, 27, var(--tw-text-opacity, 1));
}

.text-red-900 {
  --tw-text-opacity: 1;
  color: rgba(127, 29, 29, var(--tw-text-opacity, 1));
}

.text-secondary {
  --tw-text-opacity: 1;
  color: rgba(99, 102, 241, var(--tw-text-opacity, 1));
}

.text-teal-600 {
  --tw-text-opacity: 1;
  color: rgba(13, 148, 136, var(--tw-text-opacity, 1));
}

.text-teal-800 {
  --tw-text-opacity: 1;
  color: rgba(17, 94, 89, var(--tw-text-opacity, 1));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, var(--tw-text-opacity, 1));
}

.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgba(250, 204, 21, var(--tw-text-opacity, 1));
}

.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgba(234, 179, 8, var(--tw-text-opacity, 1));
}

.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgba(202, 138, 4, var(--tw-text-opacity, 1));
}

.text-yellow-700 {
  --tw-text-opacity: 1;
  color: rgba(161, 98, 7, var(--tw-text-opacity, 1));
}

.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgba(133, 77, 14, var(--tw-text-opacity, 1));
}

.text-yellow-900 {
  --tw-text-opacity: 1;
  color: rgba(113, 63, 18, var(--tw-text-opacity, 1));
}

.underline {
  -webkit-text-decoration-line: underline;
  text-decoration-line: underline;
}

.no-underline {
  -webkit-text-decoration-line: none;
  text-decoration-line: none;
}

.underline-offset-4 {
  text-underline-offset: 4px;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.placeholder-gray-400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgba(156, 163, 175, var(--tw-placeholder-opacity, 1));
}

.placeholder-gray-500::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgba(107, 114, 128, var(--tw-placeholder-opacity, 1));
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-20 {
  opacity: .2;
}

.opacity-25 {
  opacity: .25;
}

.opacity-5 {
  opacity: .05;
}

.opacity-50 {
  opacity: .5;
}

.opacity-60 {
  opacity: .6;
}

.opacity-70 {
  opacity: .7;
}

.opacity-75 {
  opacity: .75;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px -1px rgba(0, 0, 0, .1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, .25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, .1), 0 8px 10px -6px rgba(0, 0, 0, .1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.outline-none {
  outline-offset: 2px;
  outline: 2px solid rgba(0, 0, 0, 0);
}

.outline {
  outline-style: solid;
}

.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
}

.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
}

.ring-4 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
}

.ring-black {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(0, 0, 0, var(--tw-ring-opacity, 1));
}

.ring-red-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(239, 68, 68, var(--tw-ring-opacity, 1));
}

.ring-white {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(255, 255, 255, var(--tw-ring-opacity, 1));
}

.ring-opacity-5 {
  --tw-ring-opacity: .05;
}

.ring-offset-white {
  --tw-ring-offset-color: #fff;
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-lg {
  --tw-blur: blur(16px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.brightness-0 {
  --tw-brightness: brightness(0);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow-lg {
  --tw-drop-shadow: drop-shadow(0 10px 8px rgba(0, 0, 0, .04)) drop-shadow(0 4px 3px rgba(0, 0, 0, .1));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.invert {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter, backdrop-filter;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-all {
  transition-property: all;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-colors {
  transition-property: color, background-color, border-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-opacity {
  transition-property: opacity;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-shadow {
  transition-property: box-shadow;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-transform {
  transition-property: transform;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.duration-1000 {
  transition-duration: 1s;
}

.duration-150 {
  transition-duration: .15s;
}

.duration-200 {
  transition-duration: .2s;
}

.duration-300 {
  transition-duration: .3s;
}

.duration-500 {
  transition-duration: .5s;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, .2, 1);
}

:root {
  --background: #f9fafb;
  --foreground: #171717;
  --primary: #e02b20;
  --secondary: #ce4721;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #111827;
    --foreground: #ededed;
    --primary: #e02b20;
  }
}

html, body {
  background: var(--background);
  color: var(--foreground);
  height: 100%;
  font-family: Inter, sans-serif;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #dc2626;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #dc2626;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #b91c1c;
}

::-webkit-scrollbar-thumb:hover {
  background: #b91c1c;
}

.dark ::-webkit-scrollbar-track {
  background: #374151;
}

.dark ::-webkit-scrollbar-track {
  background: #374151;
}

.dark ::-webkit-scrollbar-thumb {
  background: #dc2626;
}

.dark ::-webkit-scrollbar-thumb {
  background: #dc2626;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #ef4444;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #ef4444;
}

html, * {
  scrollbar-width: thin;
  scrollbar-color: #dc2626 #f1f1f1;
}

.dark, .dark * {
  scrollbar-color: #dc2626 #374151;
}

.bg-primary, .hover\:bg-primary:hover {
  background-color: var(--primary);
}

.rounded-button {
  border-radius: .375rem;
}

.logo {
  width: 100px;
  height: auto;
}

.sidebar-logo {
  -o-object-fit: contain;
  object-fit: contain;
  width: auto;
  max-width: 120px;
  height: 2.5rem;
}

input[type="checkbox"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
  background-color: #fff;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  width: 1.25rem;
  height: 1.25rem;
  position: relative;
}

.dark input[type="checkbox"] {
  background-color: #374151;
  border-color: #4b5563;
}

input[type="checkbox"]:checked {
  background-color: #e02b20;
  border-color: #e02b20;
}

input[type="checkbox"]:checked:after {
  content: "";
  border: 2px solid #fff;
  border-width: 0 2px 2px 0;
  width: 5px;
  height: 10px;
  position: absolute;
  top: 2px;
  left: 6px;
  transform: rotate(45deg);
}

.custom-switch {
  width: 44px;
  height: 24px;
  display: inline-block;
  position: relative;
}

.custom-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-slider {
  cursor: pointer;
  background-color: #e5e7eb;
  border-radius: 34px;
  transition: all .4s;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.switch-slider:before {
  content: "";
  background-color: #fff;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  transition: all .4s;
  position: absolute;
  bottom: 3px;
  left: 3px;
}

input:checked + .switch-slider {
  background-color: #4f46e5;
}

input:checked + .switch-slider:before {
  transform: translateX(20px);
}

.custom-range {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: #e5e7eb;
  border-radius: 5px;
  outline: none;
  width: 100%;
  height: 6px;
}

.custom-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
  background: #4f46e5;
  border-radius: 50%;
  width: 18px;
  height: 18px;
}

.custom-range::-moz-range-thumb {
  cursor: pointer;
  background: #4f46e5;
  border: none;
  border-radius: 50%;
  width: 18px;
  height: 18px;
}

.custom-radio {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  width: 1.25rem;
  height: 1.25rem;
  position: relative;
}

.custom-radio:checked {
  border-color: #4f46e5;
}

.custom-radio:checked:after {
  content: "";
  background-color: #4f46e5;
  border-radius: 50%;
  width: .625rem;
  height: .625rem;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

.tab-button {
  z-index: 1;
  position: relative;
}

.tab-button.active {
  color: #e02b20;
  font-weight: 500;
}

.tab-button.active:after {
  content: "";
  background-color: #e02b20;
  width: 100%;
  height: 2px;
  position: absolute;
  bottom: -6px;
  left: 0;
}

.tab-content {
  display: block;
}

.tab-content.hidden {
  display: none;
}

.license-card {
  transition: transform .3s;
}

.license-card:hover {
  transform: translateY(-4px);
}

.dropdown-content {
  z-index: 50;
  display: none;
  position: absolute;
  top: 100%;
  right: 0;
}

.dropdown-content.show {
  display: block;
}

.enhanced-input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: #f9fafb;
  border: 2px solid #d1d5db;
  border-radius: .375rem;
  width: 100%;
  padding: .75rem 1rem;
  font-size: .875rem;
  line-height: 1.25rem;
  transition: all .15s ease-in-out;
  display: block;
  box-shadow: 0 1px 2px rgba(0, 0, 0, .05);
}

.enhanced-input:hover {
  background-color: #fff;
}

.enhanced-input:focus {
  box-shadow: 0 0 0 2px var(--primary);
  border-color: var(--primary);
  outline: none;
}

.enhanced-input::placeholder {
  color: #9ca3af;
}

.enhanced-select {
  background-color: #f9fafb;
  border: 2px solid #d1d5db;
  border-radius: .375rem;
  width: 100%;
  padding: .75rem 1rem;
  font-size: .875rem;
  line-height: 1.25rem;
  transition: all .15s ease-in-out;
  display: block;
  box-shadow: 0 1px 2px rgba(0, 0, 0, .05);
}

.enhanced-select:hover {
  background-color: #fff;
}

.enhanced-select:focus {
  box-shadow: 0 0 0 2px var(--primary);
  border-color: var(--primary);
  outline: none;
}

.enhanced-checkbox {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--primary);
  background-color: #fff;
  border: 2px solid #d1d5db;
  border-radius: .25rem;
}

.dark .enhanced-checkbox {
  background-color: #374151;
  border-color: #4b5563;
}

.enhanced-checkbox:focus {
  box-shadow: 0 0 0 2px var(--primary);
}

.enhanced-button {
  color: #fff;
  background-color: var(--primary);
  border: 2px solid rgba(0, 0, 0, 0);
  border-radius: .375rem;
  justify-content: center;
  padding: .75rem 1rem;
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.25rem;
  transition: all .15s ease-in-out;
  display: flex;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -1px rgba(0, 0, 0, .06);
}

.enhanced-button:hover {
  opacity: .9;
}

.enhanced-button:focus {
  box-shadow: 0 0 0 2px var(--primary), 0 0 0 4px rgba(224, 43, 32, .2);
  outline: none;
}

.dashboard-grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  display: grid;
}

.side-nav {
  -ms-overflow-style: none;
  height: 85vh;
  overflow: auto;
}

.side-nav::-webkit-scrollbar {
  display: none;
}

.side-nav {
  scrollbar-width: none;
}

@media (max-width: 768px) {
  .mobile-sidebar-open {
    z-index: 50;
    height: 100vh;
    position: fixed;
    box-shadow: 0 0 15px rgba(0, 0, 0, .1);
    display: block !important;
  }

  .mobile-sidebar-overlay {
    z-index: 40;
    background-color: rgba(0, 0, 0, .5);
    width: 100%;
    height: 100%;
    display: none;
    position: fixed;
    top: 0;
    left: 0;
  }

  .mobile-sidebar-overlay.show {
    display: block;
  }
}

.btn-active-primary {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-border-opacity: 1;
  border-width: 1px;
  border-color: rgba(224, 43, 32, var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgba(75, 85, 99, var(--tw-text-opacity, 1));
  border-radius: 9999px;
  align-items: center;
  padding: .5rem 1rem;
  font-size: .75rem;
  font-weight: 500;
  line-height: 1rem;
  transition-property: color, background-color, border-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter, backdrop-filter;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  display: inline-flex;
}

.btn-active-primary:hover {
  background-color: var(--primary);
  --tw-translate-x: .25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-bg-opacity: 1;
  background-color: rgba(224, 43, 32, var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, var(--tw-text-opacity, 1));
}

.btn-active-primary:after {
  content: "_↗";
}

.btn-active-delete {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-border-opacity: 1;
  border-width: 1px;
  border-color: rgba(229, 231, 235, var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgba(209, 213, 219, var(--tw-text-opacity, 1));
  border-radius: 9999px;
  align-items: center;
  padding: .5rem 1rem;
  font-size: .75rem;
  font-weight: 500;
  line-height: 1rem;
  transition-property: color, background-color, border-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter, backdrop-filter;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  display: inline-flex;
}

.btn-active-delete:hover {
  --tw-translate-x: .25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-text-opacity: 1;
  color: rgba(209, 213, 219, var(--tw-text-opacity, 1));
}

.btn-active-delete:after {
  content: "_↗";
}

.table-responsive {
  table-layout: auto;
  min-width: 100%;
}

.table-responsive > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
  --tw-divide-opacity: 1;
  border-color: rgba(229, 231, 235, var(--tw-divide-opacity, 1));
}

@media (min-width: 768px) {
  .table-responsive {
    border-collapse: separate;
  }
}

.placeholder\:text-gray-500::placeholder {
  --tw-text-opacity: 1;
  color: rgba(107, 114, 128, var(--tw-text-opacity, 1));
}

.after\:absolute:after {
  content: var(--tw-content);
  position: absolute;
}

.after\:-bottom-2:after {
  content: var(--tw-content);
  bottom: -.5rem;
}

.after\:left-1\/2:after {
  content: var(--tw-content);
  left: 50%;
}

.after\:h-0\.5:after {
  content: var(--tw-content);
  height: .125rem;
}

.after\:w-20:after {
  content: var(--tw-content);
  width: 5rem;
}

.after\:-translate-x-1\/2:after {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:transform:after {
  content: var(--tw-content);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:rounded-sm:after {
  content: var(--tw-content);
  border-radius: 4px;
}

.after\:bg-red-600:after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgba(220, 38, 38, var(--tw-bg-opacity, 1));
}

.after\:content-\[\'\'\]:after {
  --tw-content: "";
  content: var(--tw-content);
}

.after\:content-\[\'_↗\'\]:after {
  --tw-content: " ↗";
  content: var(--tw-content);
}

.last\:border-b-0:last-child {
  border-bottom-width: 0;
}

.focus-within\:outline-none:focus-within {
  outline-offset: 2px;
  outline: 2px solid rgba(0, 0, 0, 0);
}

.focus-within\:ring-2:focus-within {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
}

.focus-within\:ring-primary:focus-within {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(224, 43, 32, var(--tw-ring-opacity, 1));
}

.focus-within\:ring-offset-2:focus-within {
  --tw-ring-offset-width: 2px;
}

.hover\:-translate-x-1:hover {
  --tw-translate-x: -.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-0\.5:hover {
  --tw-translate-y: -.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-1:hover {
  --tw-translate-y: -.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:translate-x-1:hover {
  --tw-translate-x: .25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:transform:hover {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgba(209, 213, 219, var(--tw-border-opacity, 1));
}

.hover\:border-gray-400:hover {
  --tw-border-opacity: 1;
  border-color: rgba(156, 163, 175, var(--tw-border-opacity, 1));
}

.hover\:border-primary:hover {
  --tw-border-opacity: 1;
  border-color: rgba(224, 43, 32, var(--tw-border-opacity, 1));
}

.hover\:border-primary\/20:hover {
  border-color: rgba(224, 43, 32, .2);
}

.hover\:border-red-400:hover {
  --tw-border-opacity: 1;
  border-color: rgba(248, 113, 113, var(--tw-border-opacity, 1));
}

.hover\:border-red-600:hover {
  --tw-border-opacity: 1;
  border-color: rgba(220, 38, 38, var(--tw-border-opacity, 1));
}

.hover\:bg-black\/10:hover {
  background-color: rgba(0, 0, 0, .1);
}

.hover\:bg-black\/5:hover {
  background-color: rgba(0, 0, 0, .05);
}

.hover\:bg-blue-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(219, 234, 254, var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(191, 219, 254, var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(239, 246, 255, var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(29, 78, 216, var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(243, 244, 246, var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100\/50:hover {
  background-color: rgba(243, 244, 246, .5);
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(229, 231, 235, var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(209, 213, 219, var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(249, 250, 251, var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(55, 65, 81, var(--tw-bg-opacity, 1));
}

.hover\:bg-green-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(220, 252, 231, var(--tw-bg-opacity, 1));
}

.hover\:bg-green-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(187, 247, 208, var(--tw-bg-opacity, 1));
}

.hover\:bg-green-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(240, 253, 244, var(--tw-bg-opacity, 1));
}

.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(22, 163, 74, var(--tw-bg-opacity, 1));
}

.hover\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(21, 128, 61, var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(238, 242, 255, var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(67, 56, 202, var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(194, 65, 12, var(--tw-bg-opacity, 1));
}

.hover\:bg-primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(224, 43, 32, var(--tw-bg-opacity, 1));
}

.hover\:bg-primary\/10:hover {
  background-color: rgba(224, 43, 32, .1);
}

.hover\:bg-primary\/20:hover {
  background-color: rgba(224, 43, 32, .2);
}

.hover\:bg-primary\/90:hover {
  background-color: rgba(224, 43, 32, .9);
}

.hover\:bg-purple-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(250, 245, 255, var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(126, 34, 206, var(--tw-bg-opacity, 1));
}

.hover\:bg-red-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(254, 226, 226, var(--tw-bg-opacity, 1));
}

.hover\:bg-red-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(254, 202, 202, var(--tw-bg-opacity, 1));
}

.hover\:bg-red-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(254, 242, 242, var(--tw-bg-opacity, 1));
}

.hover\:bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(220, 38, 38, var(--tw-bg-opacity, 1));
}

.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(185, 28, 28, var(--tw-bg-opacity, 1));
}

.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(254, 240, 138, var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(202, 138, 4, var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(161, 98, 7, var(--tw-bg-opacity, 1));
}

.hover\:bg-opacity-90:hover {
  --tw-bg-opacity: .9;
}

.hover\:from-red-700:hover {
  --tw-gradient-from: #b91c1c var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(185, 28, 28, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:to-orange-700:hover {
  --tw-gradient-to: #c2410c var(--tw-gradient-to-position);
}

.hover\:text-blue-500:hover {
  --tw-text-opacity: 1;
  color: rgba(59, 130, 246, var(--tw-text-opacity, 1));
}

.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgba(37, 99, 235, var(--tw-text-opacity, 1));
}

.hover\:text-blue-700:hover {
  --tw-text-opacity: 1;
  color: rgba(29, 78, 216, var(--tw-text-opacity, 1));
}

.hover\:text-blue-800:hover {
  --tw-text-opacity: 1;
  color: rgba(30, 64, 175, var(--tw-text-opacity, 1));
}

.hover\:text-blue-900:hover {
  --tw-text-opacity: 1;
  color: rgba(30, 58, 138, var(--tw-text-opacity, 1));
}

.hover\:text-gray-200:hover {
  --tw-text-opacity: 1;
  color: rgba(229, 231, 235, var(--tw-text-opacity, 1));
}

.hover\:text-gray-300:hover {
  --tw-text-opacity: 1;
  color: rgba(209, 213, 219, var(--tw-text-opacity, 1));
}

.hover\:text-gray-500:hover {
  --tw-text-opacity: 1;
  color: rgba(107, 114, 128, var(--tw-text-opacity, 1));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgba(75, 85, 99, var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgba(55, 65, 81, var(--tw-text-opacity, 1));
}

.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgba(31, 41, 55, var(--tw-text-opacity, 1));
}

.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgba(17, 24, 39, var(--tw-text-opacity, 1));
}

.hover\:text-green-600:hover {
  --tw-text-opacity: 1;
  color: rgba(22, 163, 74, var(--tw-text-opacity, 1));
}

.hover\:text-green-700:hover {
  --tw-text-opacity: 1;
  color: rgba(21, 128, 61, var(--tw-text-opacity, 1));
}

.hover\:text-green-800:hover {
  --tw-text-opacity: 1;
  color: rgba(22, 101, 52, var(--tw-text-opacity, 1));
}

.hover\:text-green-900:hover {
  --tw-text-opacity: 1;
  color: rgba(20, 83, 45, var(--tw-text-opacity, 1));
}

.hover\:text-indigo-900:hover {
  --tw-text-opacity: 1;
  color: rgba(49, 46, 129, var(--tw-text-opacity, 1));
}

.hover\:text-primary:hover {
  --tw-text-opacity: 1;
  color: rgba(224, 43, 32, var(--tw-text-opacity, 1));
}

.hover\:text-purple-700:hover {
  --tw-text-opacity: 1;
  color: rgba(126, 34, 206, var(--tw-text-opacity, 1));
}

.hover\:text-purple-800:hover {
  --tw-text-opacity: 1;
  color: rgba(107, 33, 168, var(--tw-text-opacity, 1));
}

.hover\:text-purple-900:hover {
  --tw-text-opacity: 1;
  color: rgba(88, 28, 135, var(--tw-text-opacity, 1));
}

.hover\:text-red-500:hover {
  --tw-text-opacity: 1;
  color: rgba(239, 68, 68, var(--tw-text-opacity, 1));
}

.hover\:text-red-600:hover {
  --tw-text-opacity: 1;
  color: rgba(220, 38, 38, var(--tw-text-opacity, 1));
}

.hover\:text-red-700:hover {
  --tw-text-opacity: 1;
  color: rgba(185, 28, 28, var(--tw-text-opacity, 1));
}

.hover\:text-red-800:hover {
  --tw-text-opacity: 1;
  color: rgba(153, 27, 27, var(--tw-text-opacity, 1));
}

.hover\:text-red-900:hover {
  --tw-text-opacity: 1;
  color: rgba(127, 29, 29, var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, var(--tw-text-opacity, 1));
}

.hover\:text-yellow-600:hover {
  --tw-text-opacity: 1;
  color: rgba(202, 138, 4, var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  -webkit-text-decoration-line: underline;
  text-decoration-line: underline;
}

.hover\:no-underline:hover {
  -webkit-text-decoration-line: none;
  text-decoration-line: none;
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.hover\:shadow-sm:hover {
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, .1), 0 8px 10px -6px rgba(0, 0, 0, .1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.focus\:border-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: rgba(59, 130, 246, var(--tw-border-opacity, 1));
}

.focus\:border-green-500:focus {
  --tw-border-opacity: 1;
  border-color: rgba(34, 197, 94, var(--tw-border-opacity, 1));
}

.focus\:border-primary:focus {
  --tw-border-opacity: 1;
  border-color: rgba(224, 43, 32, var(--tw-border-opacity, 1));
}

.focus\:border-red-500:focus {
  --tw-border-opacity: 1;
  border-color: rgba(239, 68, 68, var(--tw-border-opacity, 1));
}

.focus\:border-transparent:focus {
  border-color: rgba(0, 0, 0, 0);
}

.focus\:bg-gray-100:focus {
  --tw-bg-opacity: 1;
  background-color: rgba(243, 244, 246, var(--tw-bg-opacity, 1));
}

.focus\:placeholder-gray-400:focus::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgba(156, 163, 175, var(--tw-placeholder-opacity, 1));
}

.focus\:shadow-md:focus {
  --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.focus\:outline-none:focus {
  outline-offset: 2px;
  outline: 2px solid rgba(0, 0, 0, 0);
}

.focus\:ring:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
}

.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
}

.focus\:ring-inset:focus {
  --tw-ring-inset: inset;
}

.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(59, 130, 246, var(--tw-ring-opacity, 1));
}

.focus\:ring-current:focus {
  --tw-ring-color: currentColor;
}

.focus\:ring-gray-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(107, 114, 128, var(--tw-ring-opacity, 1));
}

.focus\:ring-green-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(34, 197, 94, var(--tw-ring-opacity, 1));
}

.focus\:ring-indigo-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(99, 102, 241, var(--tw-ring-opacity, 1));
}

.focus\:ring-orange-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(249, 115, 22, var(--tw-ring-opacity, 1));
}

.focus\:ring-primary:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(224, 43, 32, var(--tw-ring-opacity, 1));
}

.focus\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(239, 68, 68, var(--tw-ring-opacity, 1));
}

.focus\:ring-secondary:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(99, 102, 241, var(--tw-ring-opacity, 1));
}

.focus\:ring-white:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(255, 255, 255, var(--tw-ring-opacity, 1));
}

.focus\:ring-yellow-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(234, 179, 8, var(--tw-ring-opacity, 1));
}

.focus\:ring-opacity-50:focus {
  --tw-ring-opacity: .5;
}

.focus\:ring-offset-1:focus {
  --tw-ring-offset-width: 1px;
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus-visible\:outline-none:focus-visible {
  outline-offset: 2px;
  outline: 2px solid rgba(0, 0, 0, 0);
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
}

.focus-visible\:ring-red-500:focus-visible {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(239, 68, 68, var(--tw-ring-opacity, 1));
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:bg-gray-50:disabled {
  --tw-bg-opacity: 1;
  background-color: rgba(249, 250, 251, var(--tw-bg-opacity, 1));
}

.disabled\:bg-gray-400:disabled {
  --tw-bg-opacity: 1;
  background-color: rgba(156, 163, 175, var(--tw-bg-opacity, 1));
}

.disabled\:text-gray-400:disabled {
  --tw-text-opacity: 1;
  color: rgba(156, 163, 175, var(--tw-text-opacity, 1));
}

.disabled\:text-gray-500:disabled {
  --tw-text-opacity: 1;
  color: rgba(107, 114, 128, var(--tw-text-opacity, 1));
}

.disabled\:opacity-50:disabled {
  opacity: .5;
}

.group:hover .group-hover\:translate-x-1 {
  --tw-translate-x: .25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:bg-red-700 {
  --tw-bg-opacity: 1;
  background-color: rgba(185, 28, 28, var(--tw-bg-opacity, 1));
}

.group:hover .group-hover\:text-blue-600 {
  --tw-text-opacity: 1;
  color: rgba(37, 99, 235, var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-primary {
  --tw-text-opacity: 1;
  color: rgba(224, 43, 32, var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}

.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: .7;
}

.data-\[state\=selected\]\:bg-gray-100[data-state="selected"] {
  --tw-bg-opacity: 1;
  background-color: rgba(243, 244, 246, var(--tw-bg-opacity, 1));
}

.dark\:block:is(.dark *) {
  display: block;
}

.dark\:hidden:is(.dark *) {
  display: none;
}

.dark\:divide-gray-600:is(.dark *) > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgba(75, 85, 99, var(--tw-divide-opacity, 1));
}

.dark\:divide-gray-700:is(.dark *) > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgba(55, 65, 81, var(--tw-divide-opacity, 1));
}

.dark\:border-blue-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(37, 99, 235, var(--tw-border-opacity, 1));
}

.dark\:border-blue-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(30, 64, 175, var(--tw-border-opacity, 1));
}

.dark\:border-gray-500:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(107, 114, 128, var(--tw-border-opacity, 1));
}

.dark\:border-gray-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(75, 85, 99, var(--tw-border-opacity, 1));
}

.dark\:border-gray-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(55, 65, 81, var(--tw-border-opacity, 1));
}

.dark\:border-gray-700\/50:is(.dark *) {
  border-color: rgba(55, 65, 81, .5);
}

.dark\:border-gray-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(31, 41, 55, var(--tw-border-opacity, 1));
}

.dark\:border-green-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(22, 163, 74, var(--tw-border-opacity, 1));
}

.dark\:border-green-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(22, 101, 52, var(--tw-border-opacity, 1));
}

.dark\:border-orange-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(154, 52, 18, var(--tw-border-opacity, 1));
}

.dark\:border-purple-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(147, 51, 234, var(--tw-border-opacity, 1));
}

.dark\:border-red-400:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(248, 113, 113, var(--tw-border-opacity, 1));
}

.dark\:border-red-500:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(239, 68, 68, var(--tw-border-opacity, 1));
}

.dark\:border-red-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(220, 38, 38, var(--tw-border-opacity, 1));
}

.dark\:border-red-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(185, 28, 28, var(--tw-border-opacity, 1));
}

.dark\:border-red-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(153, 27, 27, var(--tw-border-opacity, 1));
}

.dark\:border-yellow-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(133, 77, 14, var(--tw-border-opacity, 1));
}

.dark\:bg-black:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(0, 0, 0, var(--tw-bg-opacity, 1));
}

.dark\:bg-blue-400:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(96, 165, 250, var(--tw-bg-opacity, 1));
}

.dark\:bg-blue-500:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(59, 130, 246, var(--tw-bg-opacity, 1));
}

.dark\:bg-blue-600:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(37, 99, 235, var(--tw-bg-opacity, 1));
}

.dark\:bg-blue-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(30, 58, 138, var(--tw-bg-opacity, 1));
}

.dark\:bg-blue-900\/20:is(.dark *) {
  background-color: rgba(30, 58, 138, .2);
}

.dark\:bg-blue-900\/30:is(.dark *) {
  background-color: rgba(30, 58, 138, .3);
}

.dark\:bg-blue-900\/40:is(.dark *) {
  background-color: rgba(30, 58, 138, .4);
}

.dark\:bg-blue-900\/50:is(.dark *) {
  background-color: rgba(30, 58, 138, .5);
}

.dark\:bg-emerald-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(6, 78, 59, var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-600:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(75, 85, 99, var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(55, 65, 81, var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-700\/50:is(.dark *) {
  background-color: rgba(55, 65, 81, .5);
}

.dark\:bg-gray-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(31, 41, 55, var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-800\/50:is(.dark *) {
  background-color: rgba(31, 41, 55, .5);
}

.dark\:bg-gray-800\/80:is(.dark *) {
  background-color: rgba(31, 41, 55, .8);
}

.dark\:bg-gray-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(17, 24, 39, var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-900\/20:is(.dark *) {
  background-color: rgba(17, 24, 39, .2);
}

.dark\:bg-gray-900\/50:is(.dark *) {
  background-color: rgba(17, 24, 39, .5);
}

.dark\:bg-green-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(22, 101, 52, var(--tw-bg-opacity, 1));
}

.dark\:bg-green-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(20, 83, 45, var(--tw-bg-opacity, 1));
}

.dark\:bg-green-900\/20:is(.dark *) {
  background-color: rgba(20, 83, 45, .2);
}

.dark\:bg-green-900\/30:is(.dark *) {
  background-color: rgba(20, 83, 45, .3);
}

.dark\:bg-indigo-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(49, 46, 129, var(--tw-bg-opacity, 1));
}

.dark\:bg-indigo-900\/20:is(.dark *) {
  background-color: rgba(49, 46, 129, .2);
}

.dark\:bg-orange-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(124, 45, 18, var(--tw-bg-opacity, 1));
}

.dark\:bg-orange-900\/20:is(.dark *) {
  background-color: rgba(124, 45, 18, .2);
}

.dark\:bg-orange-900\/30:is(.dark *) {
  background-color: rgba(124, 45, 18, .3);
}

.dark\:bg-primary:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(224, 43, 32, var(--tw-bg-opacity, 1));
}

.dark\:bg-primary\/10:is(.dark *) {
  background-color: rgba(224, 43, 32, .1);
}

.dark\:bg-primary\/20:is(.dark *) {
  background-color: rgba(224, 43, 32, .2);
}

.dark\:bg-purple-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(88, 28, 135, var(--tw-bg-opacity, 1));
}

.dark\:bg-purple-900\/20:is(.dark *) {
  background-color: rgba(88, 28, 135, .2);
}

.dark\:bg-red-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(185, 28, 28, var(--tw-bg-opacity, 1));
}

.dark\:bg-red-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(153, 27, 27, var(--tw-bg-opacity, 1));
}

.dark\:bg-red-800\/30:is(.dark *) {
  background-color: rgba(153, 27, 27, .3);
}

.dark\:bg-red-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(127, 29, 29, var(--tw-bg-opacity, 1));
}

.dark\:bg-red-900\/10:is(.dark *) {
  background-color: rgba(127, 29, 29, .1);
}

.dark\:bg-red-900\/20:is(.dark *) {
  background-color: rgba(127, 29, 29, .2);
}

.dark\:bg-red-900\/30:is(.dark *) {
  background-color: rgba(127, 29, 29, .3);
}

.dark\:bg-teal-900\/20:is(.dark *) {
  background-color: rgba(19, 78, 74, .2);
}

.dark\:bg-yellow-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(113, 63, 18, var(--tw-bg-opacity, 1));
}

.dark\:bg-yellow-900\/20:is(.dark *) {
  background-color: rgba(113, 63, 18, .2);
}

.dark\:bg-yellow-900\/30:is(.dark *) {
  background-color: rgba(113, 63, 18, .3);
}

.dark\:bg-opacity-20:is(.dark *) {
  --tw-bg-opacity: .2;
}

.dark\:bg-opacity-70:is(.dark *) {
  --tw-bg-opacity: .7;
}

.dark\:bg-opacity-75:is(.dark *) {
  --tw-bg-opacity: .75;
}

.dark\:from-gray-800:is(.dark *) {
  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(31, 41, 55, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-gray-900:is(.dark *) {
  --tw-gradient-from: #111827 var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(17, 24, 39, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-primary\/10:is(.dark *) {
  --tw-gradient-from: rgba(224, 43, 32, .1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(224, 43, 32, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-red-900\/20:is(.dark *) {
  --tw-gradient-from: rgba(127, 29, 29, .2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(127, 29, 29, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-red-900\/50:is(.dark *) {
  --tw-gradient-from: rgba(127, 29, 29, .5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(127, 29, 29, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:via-gray-800:is(.dark *) {
  --tw-gradient-to: rgba(31, 41, 55, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #1f2937 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:to-blue-900:is(.dark *) {
  --tw-gradient-to: #1e3a8a var(--tw-gradient-to-position);
}

.dark\:to-gray-700:is(.dark *) {
  --tw-gradient-to: #374151 var(--tw-gradient-to-position);
}

.dark\:to-red-900:is(.dark *) {
  --tw-gradient-to: #7f1d1d var(--tw-gradient-to-position);
}

.dark\:to-red-900\/20:is(.dark *) {
  --tw-gradient-to: rgba(127, 29, 29, .2) var(--tw-gradient-to-position);
}

.dark\:to-rose-900\/20:is(.dark *) {
  --tw-gradient-to: rgba(136, 19, 55, .2) var(--tw-gradient-to-position);
}

.dark\:to-rose-900\/50:is(.dark *) {
  --tw-gradient-to: rgba(136, 19, 55, .5) var(--tw-gradient-to-position);
}

.dark\:text-amber-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(251, 191, 36, var(--tw-text-opacity, 1));
}

.dark\:text-blue-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(219, 234, 254, var(--tw-text-opacity, 1));
}

.dark\:text-blue-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(191, 219, 254, var(--tw-text-opacity, 1));
}

.dark\:text-blue-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(147, 197, 253, var(--tw-text-opacity, 1));
}

.dark\:text-blue-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(96, 165, 250, var(--tw-text-opacity, 1));
}

.dark\:text-blue-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(59, 130, 246, var(--tw-text-opacity, 1));
}

.dark\:text-emerald-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(167, 243, 208, var(--tw-text-opacity, 1));
}

.dark\:text-gray-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(243, 244, 246, var(--tw-text-opacity, 1));
}

.dark\:text-gray-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(229, 231, 235, var(--tw-text-opacity, 1));
}

.dark\:text-gray-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(209, 213, 219, var(--tw-text-opacity, 1));
}

.dark\:text-gray-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(156, 163, 175, var(--tw-text-opacity, 1));
}

.dark\:text-gray-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(107, 114, 128, var(--tw-text-opacity, 1));
}

.dark\:text-gray-600:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(75, 85, 99, var(--tw-text-opacity, 1));
}

.dark\:text-green-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(220, 252, 231, var(--tw-text-opacity, 1));
}

.dark\:text-green-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(187, 247, 208, var(--tw-text-opacity, 1));
}

.dark\:text-green-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(134, 239, 172, var(--tw-text-opacity, 1));
}

.dark\:text-green-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(74, 222, 128, var(--tw-text-opacity, 1));
}

.dark\:text-green-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(34, 197, 94, var(--tw-text-opacity, 1));
}

.dark\:text-indigo-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(199, 210, 254, var(--tw-text-opacity, 1));
}

.dark\:text-indigo-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(129, 140, 248, var(--tw-text-opacity, 1));
}

.dark\:text-orange-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(254, 215, 170, var(--tw-text-opacity, 1));
}

.dark\:text-orange-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(251, 146, 60, var(--tw-text-opacity, 1));
}

.dark\:text-primary:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(224, 43, 32, var(--tw-text-opacity, 1));
}

.dark\:text-purple-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(233, 213, 255, var(--tw-text-opacity, 1));
}

.dark\:text-purple-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(216, 180, 254, var(--tw-text-opacity, 1));
}

.dark\:text-purple-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(192, 132, 252, var(--tw-text-opacity, 1));
}

.dark\:text-red-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(254, 226, 226, var(--tw-text-opacity, 1));
}

.dark\:text-red-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(254, 202, 202, var(--tw-text-opacity, 1));
}

.dark\:text-red-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(252, 165, 165, var(--tw-text-opacity, 1));
}

.dark\:text-red-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(248, 113, 113, var(--tw-text-opacity, 1));
}

.dark\:text-red-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(239, 68, 68, var(--tw-text-opacity, 1));
}

.dark\:text-teal-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(45, 212, 191, var(--tw-text-opacity, 1));
}

.dark\:text-white:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, var(--tw-text-opacity, 1));
}

.dark\:text-yellow-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(254, 249, 195, var(--tw-text-opacity, 1));
}

.dark\:text-yellow-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(254, 240, 138, var(--tw-text-opacity, 1));
}

.dark\:text-yellow-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(253, 224, 71, var(--tw-text-opacity, 1));
}

.dark\:text-yellow-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(250, 204, 21, var(--tw-text-opacity, 1));
}

.dark\:placeholder-gray-400:is(.dark *)::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgba(156, 163, 175, var(--tw-placeholder-opacity, 1));
}

.dark\:placeholder-gray-400:is(.dark *)::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgba(156, 163, 175, var(--tw-placeholder-opacity, 1));
}

.dark\:placeholder-gray-500:is(.dark *)::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgba(107, 114, 128, var(--tw-placeholder-opacity, 1));
}

.dark\:placeholder-gray-500:is(.dark *)::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgba(107, 114, 128, var(--tw-placeholder-opacity, 1));
}

.dark\:opacity-10:is(.dark *) {
  opacity: .1;
}

.dark\:ring-gray-600:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(75, 85, 99, var(--tw-ring-opacity, 1));
}

.dark\:ring-gray-700:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(55, 65, 81, var(--tw-ring-opacity, 1));
}

.dark\:ring-gray-800:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(31, 41, 55, var(--tw-ring-opacity, 1));
}

.dark\:ring-offset-gray-800:is(.dark *) {
  --tw-ring-offset-color: #1f2937;
}

.dark\:ring-offset-gray-950:is(.dark *) {
  --tw-ring-offset-color: #030712;
}

.dark\:placeholder\:text-gray-400:is(.dark *)::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgba(156, 163, 175, var(--tw-text-opacity, 1));
}

.dark\:placeholder\:text-gray-400:is(.dark *)::placeholder {
  --tw-text-opacity: 1;
  color: rgba(156, 163, 175, var(--tw-text-opacity, 1));
}

.dark\:hover\:border-gray-500:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(107, 114, 128, var(--tw-border-opacity, 1));
}

.dark\:hover\:border-gray-600:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(75, 85, 99, var(--tw-border-opacity, 1));
}

.dark\:hover\:border-red-500:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(239, 68, 68, var(--tw-border-opacity, 1));
}

.dark\:hover\:bg-blue-900\/20:hover:is(.dark *) {
  background-color: rgba(30, 58, 138, .2);
}

.dark\:hover\:bg-blue-900\/30:hover:is(.dark *) {
  background-color: rgba(30, 58, 138, .3);
}

.dark\:hover\:bg-blue-900\/40:hover:is(.dark *) {
  background-color: rgba(30, 58, 138, .4);
}

.dark\:hover\:bg-blue-900\/50:hover:is(.dark *) {
  background-color: rgba(30, 58, 138, .5);
}

.dark\:hover\:bg-gray-500:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(107, 114, 128, var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-gray-600:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(75, 85, 99, var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-gray-700:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(55, 65, 81, var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-gray-700\/50:hover:is(.dark *) {
  background-color: rgba(55, 65, 81, .5);
}

.dark\:hover\:bg-gray-800:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(31, 41, 55, var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-gray-800\/50:hover:is(.dark *) {
  background-color: rgba(31, 41, 55, .5);
}

.dark\:hover\:bg-green-900:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(20, 83, 45, var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-green-900\/20:hover:is(.dark *) {
  background-color: rgba(20, 83, 45, .2);
}

.dark\:hover\:bg-green-900\/30:hover:is(.dark *) {
  background-color: rgba(20, 83, 45, .3);
}

.dark\:hover\:bg-green-900\/40:hover:is(.dark *) {
  background-color: rgba(20, 83, 45, .4);
}

.dark\:hover\:bg-indigo-900:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(49, 46, 129, var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-purple-900:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(88, 28, 135, var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-purple-900\/20:hover:is(.dark *) {
  background-color: rgba(88, 28, 135, .2);
}

.dark\:hover\:bg-red-700:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(185, 28, 28, var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-red-800:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(153, 27, 27, var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-red-900:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(127, 29, 29, var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-red-900\/20:hover:is(.dark *) {
  background-color: rgba(127, 29, 29, .2);
}

.dark\:hover\:bg-red-900\/30:hover:is(.dark *) {
  background-color: rgba(127, 29, 29, .3);
}

.dark\:hover\:bg-red-900\/40:hover:is(.dark *) {
  background-color: rgba(127, 29, 29, .4);
}

.dark\:hover\:bg-white\/10:hover:is(.dark *) {
  background-color: rgba(255, 255, 255, .1);
}

.dark\:hover\:bg-white\/5:hover:is(.dark *) {
  background-color: rgba(255, 255, 255, .05);
}

.dark\:hover\:text-blue-100:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(219, 234, 254, var(--tw-text-opacity, 1));
}

.dark\:hover\:text-blue-200:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(191, 219, 254, var(--tw-text-opacity, 1));
}

.dark\:hover\:text-blue-300:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(147, 197, 253, var(--tw-text-opacity, 1));
}

.dark\:hover\:text-blue-400:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(96, 165, 250, var(--tw-text-opacity, 1));
}

.dark\:hover\:text-gray-100:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(243, 244, 246, var(--tw-text-opacity, 1));
}

.dark\:hover\:text-gray-200:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(229, 231, 235, var(--tw-text-opacity, 1));
}

.dark\:hover\:text-gray-300:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(209, 213, 219, var(--tw-text-opacity, 1));
}

.dark\:hover\:text-green-200:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(187, 247, 208, var(--tw-text-opacity, 1));
}

.dark\:hover\:text-green-300:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(134, 239, 172, var(--tw-text-opacity, 1));
}

.dark\:hover\:text-indigo-300:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(165, 180, 252, var(--tw-text-opacity, 1));
}

.dark\:hover\:text-primary:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(224, 43, 32, var(--tw-text-opacity, 1));
}

.dark\:hover\:text-purple-300:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(216, 180, 254, var(--tw-text-opacity, 1));
}

.dark\:hover\:text-red-200:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(254, 202, 202, var(--tw-text-opacity, 1));
}

.dark\:hover\:text-red-300:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(252, 165, 165, var(--tw-text-opacity, 1));
}

.dark\:hover\:text-red-400:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(248, 113, 113, var(--tw-text-opacity, 1));
}

.dark\:hover\:text-yellow-100:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(254, 249, 195, var(--tw-text-opacity, 1));
}

.dark\:focus\:border-primary:focus:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgba(224, 43, 32, var(--tw-border-opacity, 1));
}

.dark\:focus\:bg-gray-700:focus:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(55, 65, 81, var(--tw-bg-opacity, 1));
}

.dark\:focus\:ring-primary:focus:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(224, 43, 32, var(--tw-ring-opacity, 1));
}

.dark\:focus\:ring-offset-gray-800:focus:is(.dark *) {
  --tw-ring-offset-color: #1f2937;
}

.dark\:focus\:ring-offset-gray-900:focus:is(.dark *) {
  --tw-ring-offset-color: #111827;
}

.dark\:disabled\:bg-gray-800:disabled:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(31, 41, 55, var(--tw-bg-opacity, 1));
}

.group:hover .dark\:group-hover\:text-blue-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(96, 165, 250, var(--tw-text-opacity, 1));
}

.dark\:data-\[state\=selected\]\:bg-gray-800[data-state="selected"]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgba(31, 41, 55, var(--tw-bg-opacity, 1));
}

@media (min-width: 640px) {
  .sm\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .sm\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .sm\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .sm\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .sm\:-mx-6 {
    margin-left: -1.5rem;
    margin-right: -1.5rem;
  }

  .sm\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .sm\:my-8 {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  .sm\:ml-3 {
    margin-left: .75rem;
  }

  .sm\:ml-4 {
    margin-left: 1rem;
  }

  .sm\:ml-6 {
    margin-left: 1.5rem;
  }

  .sm\:mr-3 {
    margin-right: .75rem;
  }

  .sm\:mt-0 {
    margin-top: 0;
  }

  .sm\:mt-8 {
    margin-top: 2rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:inline {
    display: inline;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:w-48 {
    width: 12rem;
  }

  .sm\:w-64 {
    width: 16rem;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-full {
    width: 100%;
  }

  .sm\:max-w-2xl {
    max-width: 42rem;
  }

  .sm\:max-w-3xl {
    max-width: 48rem;
  }

  .sm\:max-w-4xl {
    max-width: 56rem;
  }

  .sm\:max-w-\[500px\] {
    max-width: 500px;
  }

  .sm\:max-w-lg {
    max-width: 32rem;
  }

  .sm\:max-w-md {
    max-width: 28rem;
  }

  .sm\:max-w-sm {
    max-width: 24rem;
  }

  .sm\:max-w-xl {
    max-width: 36rem;
  }

  .sm\:flex-1 {
    flex: 1;
  }

  .sm\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .sm\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-row-reverse {
    flex-direction: row-reverse;
  }

  .sm\:flex-wrap {
    flex-wrap: wrap;
  }

  .sm\:items-start {
    align-items: flex-start;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:justify-center {
    justify-content: center;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:gap-x-6 {
    -moz-column-gap: 1.5rem;
    column-gap: 1.5rem;
  }

  .sm\:gap-y-6 {
    row-gap: 1.5rem;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(.5rem * var(--tw-space-x-reverse));
    margin-left: calc(.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(.75rem * var(--tw-space-x-reverse));
    margin-left: calc(.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .sm\:space-y-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
  }

  .sm\:truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .sm\:break-words {
    overflow-wrap: break-word;
  }

  .sm\:rounded-lg {
    border-radius: 16px;
  }

  .sm\:rounded-md {
    border-radius: 12px;
  }

  .sm\:p-0 {
    padding: 0;
  }

  .sm\:p-6 {
    padding: 1.5rem;
  }

  .sm\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .sm\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .sm\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .sm\:pb-4 {
    padding-bottom: 1rem;
  }

  .sm\:pl-2 {
    padding-left: .5rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:align-middle {
    vertical-align: middle;
  }

  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .sm\:text-sm {
    font-size: .875rem;
    line-height: 1.25rem;
  }
}

@media (min-width: 768px) {
  .md\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:ml-0 {
    margin-left: 0;
  }

  .md\:ml-2 {
    margin-left: .5rem;
  }

  .md\:ml-4 {
    margin-left: 1rem;
  }

  .md\:block {
    display: block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:w-3\/4 {
    width: 75%;
  }

  .md\:w-4\/5 {
    width: 80%;
  }

  .md\:flex-shrink-0 {
    flex-shrink: 0;
  }

  .md\:border-separate {
    border-collapse: separate;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .md\:items-start {
    align-items: flex-start;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(.75rem * var(--tw-space-x-reverse));
    margin-left: calc(.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:p-10 {
    padding: 2.5rem;
  }

  .md\:p-8 {
    padding: 2rem;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }
}

@media (min-width: 1024px) {
  .lg\:static {
    position: static;
  }

  .lg\:relative {
    position: relative;
  }

  .lg\:sticky {
    position: -webkit-sticky;
    position: sticky;
  }

  .lg\:inset-auto {
    top: auto;
    bottom: auto;
    left: auto;
    right: auto;
  }

  .lg\:z-auto {
    z-index: auto;
  }

  .lg\:order-1 {
    order: 1;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .lg\:-mx-8 {
    margin-left: -2rem;
    margin-right: -2rem;
  }

  .lg\:ml-6 {
    margin-left: 1.5rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-auto {
    height: auto;
  }

  .lg\:max-h-\[calc\(100vh-200px\)\] {
    max-height: calc(100vh - 200px);
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:w-3\/4 {
    width: 75%;
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:items-start {
    align-items: flex-start;
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:justify-start {
    justify-content: flex-start;
  }

  .lg\:justify-between {
    justify-content: space-between;
  }

  .lg\:gap-4 {
    gap: 1rem;
  }

  .lg\:space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .lg\:border-b {
    border-bottom-width: 1px;
  }

  .lg\:bg-transparent {
    background-color: rgba(0, 0, 0, 0);
  }

  .lg\:p-6 {
    padding: 1.5rem;
  }

  .lg\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-3 {
    padding-top: .75rem;
    padding-bottom: .75rem;
  }

  .lg\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .lg\:pr-2 {
    padding-right: .5rem;
  }

  .lg\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .lg\:text-sm {
    font-size: .875rem;
    line-height: 1.25rem;
  }
}

@media (min-width: 1280px) {
  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media print {
  .print\:hidden {
    display: none;
  }
}

.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role="checkbox"]) {
  padding-right: 0;
}

.\[\&\>tr\]\:last\:border-b-0:last-child > tr {
  border-bottom-width: 0;
}

.\[\&_tr\:last-child\]\:border-0 tr:last-child {
  border-width: 0;
}

.\[\&_tr\]\:border-b tr {
  border-bottom-width: 1px;
}

/* [project]/src/styles/form-fixes.css [app-client] (css) */
input, textarea, select {
  color: #000 !important;
}

.dark input, .dark textarea, .dark select {
  color: #fff !important;
}

label {
  color: #000;
}

.dark label {
  color: #fff;
}

::placeholder {
  opacity: 1;
  color: #9ca3af !important;
}

.dark ::placeholder {
  opacity: 1;
  color: #6b7280 !important;
}

/*# sourceMappingURL=%5Broot-of-the-server%5D__b384025f._.css.map*/
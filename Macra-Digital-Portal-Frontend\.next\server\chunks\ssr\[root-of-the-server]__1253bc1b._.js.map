{"version": 3, "sources": [], "sections": [{"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/lib/auth.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\n// Authentication utility functions\r\n\r\nexport const getAuthToken = (): string | null => {\r\n  if (typeof window === 'undefined') {\r\n    return null; // Server-side rendering\r\n  }\r\n  \r\n  return localStorage.getItem('auth_token');\r\n};\r\n\r\nexport const setAuthToken = (token: string): void => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.setItem('auth_token', token);\r\n  }\r\n};\r\n\r\nexport const removeAuthToken = (): void => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.removeItem('auth_token');\r\n  }\r\n};\r\n\r\nexport const isAuthenticated = (): boolean => {\r\n  return !!getAuthToken();\r\n};\r\n\r\n\r\n\r\n// Create axios instance with auth\r\nexport const createAuthenticatedAxios = (API_BASE_URL: string = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001') => {\r\n  const token = getAuthToken();\r\n  const instance = axios.create({\r\n    baseURL: API_BASE_URL,\r\n    timeout: 10000, // 10 second timeout\r\n    headers: {\r\n      'Authorization': token ? `Bearer ${token}` : '',\r\n      'Content-Type': 'application/json',\r\n    },\r\n  });\r\n\r\n  // Add request interceptor for debugging\r\n  instance.interceptors.request.use(\r\n    (config) => {\r\n      console.log(`Making ${config.method?.toUpperCase()} request to: ${config.baseURL}${config.url}`);\r\n      return config;\r\n    },\r\n    (error) => {\r\n      console.error('Request interceptor error:', error);\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n\r\n  // Add response interceptor for better error handling\r\n  instance.interceptors.response.use(\r\n    (response) => {\r\n      return response;\r\n    },\r\n    (error) => {\r\n      console.error('API Error Details:', {\r\n        url: error.config?.url,\r\n        method: error.config?.method,\r\n        status: error.response?.status,\r\n        statusText: error.response?.statusText,\r\n        message: error.message,\r\n        code: error.code,\r\n        data: error.response?.data\r\n      });\r\n\r\n      // Handle authentication errors - auto logout on 401\r\n      if (error.response?.status === 401) {\r\n        console.warn('Authentication failed - token invalid or expired. Logging out...');\r\n        removeAuthToken();\r\n        // Redirect to login page\r\n        if (typeof window !== 'undefined') {\r\n          window.location.href = '/auth/login';\r\n        }\r\n      }\r\n\r\n      // Handle network errors\r\n      if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {\r\n        console.error('Network error - backend may not be accessible');\r\n      }\r\n\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n\r\n  return instance;\r\n};"], "names": [], "mappings": ";;;;;;;AAAA;;AAIO,MAAM,eAAe;IAC1B,wCAAmC;QACjC,OAAO,MAAM,wBAAwB;IACvC;;;AAGF;AAEO,MAAM,eAAe,CAAC;IAC3B;;AAGF;AAEO,MAAM,kBAAkB;IAC7B;;AAGF;AAEO,MAAM,kBAAkB;IAC7B,OAAO,CAAC,CAAC;AACX;AAKO,MAAM,2BAA2B,CAAC,eAAuB,6DAAmC,uBAAuB;IACxH,MAAM,QAAQ;IACd,MAAM,WAAW,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;QAC5B,SAAS;QACT,SAAS;QACT,SAAS;YACP,iBAAiB,QAAQ,CAAC,OAAO,EAAE,OAAO,GAAG;YAC7C,gBAAgB;QAClB;IACF;IAEA,wCAAwC;IACxC,SAAS,YAAY,CAAC,OAAO,CAAC,GAAG,CAC/B,CAAC;QACC,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE,cAAc,aAAa,EAAE,OAAO,OAAO,GAAG,OAAO,GAAG,EAAE;QAC/F,OAAO;IACT,GACA,CAAC;QACC,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,QAAQ,MAAM,CAAC;IACxB;IAGF,qDAAqD;IACrD,SAAS,YAAY,CAAC,QAAQ,CAAC,GAAG,CAChC,CAAC;QACC,OAAO;IACT,GACA,CAAC;QACC,QAAQ,KAAK,CAAC,sBAAsB;YAClC,KAAK,MAAM,MAAM,EAAE;YACnB,QAAQ,MAAM,MAAM,EAAE;YACtB,QAAQ,MAAM,QAAQ,EAAE;YACxB,YAAY,MAAM,QAAQ,EAAE;YAC5B,SAAS,MAAM,OAAO;YACtB,MAAM,MAAM,IAAI;YAChB,MAAM,MAAM,QAAQ,EAAE;QACxB;QAEA,oDAAoD;QACpD,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;YAClC,QAAQ,IAAI,CAAC;YACb;YACA,yBAAyB;YACzB;;QAGF;QAEA,wBAAwB;QACxB,IAAI,MAAM,IAAI,KAAK,iBAAiB,MAAM,OAAO,KAAK,iBAAiB;YACrE,QAAQ,KAAK,CAAC;QAChB;QAEA,OAAO,QAAQ,MAAM,CAAC;IACxB;IAGF,OAAO;AACT", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/lib/apiClient.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosError } from 'axios';\r\nimport { getAuthToken } from './auth';\r\nimport { forceLogout } from './authUtils';\r\nimport { apiRateLimiter, withExponentialBackoff } from '../utils/rateLimiter';\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n\r\n/**\r\n * Create an authenticated axios instance with proper error handling\r\n * This instance automatically handles 401 errors and performs auto-logout\r\n */\r\nexport const createApiClient = (baseURL: string = API_BASE_URL): AxiosInstance => {\r\n  const instance = axios.create({\r\n    baseURL,\r\n    timeout: 120000, // 120 second timeout for better reliability\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n  });\r\n\r\n  // Request interceptor to add auth token and rate limiting\r\n  instance.interceptors.request.use(\r\n    async (config) => {\r\n      const token = getAuthToken();\r\n      if (token) {\r\n        config.headers.Authorization = `Bearer ${token}`;\r\n      }\r\n      // Log request for debugging (only in development)\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.log(`[Staff API] Making ${config.method?.toUpperCase()} request to: ${config.baseURL}${config.url}`);\r\n      }\r\n      return config;\r\n    },\r\n    (error) => {\r\n      console.error('Request interceptor error:', error);\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n\r\n  // Response interceptor for error handling and auto-logout\r\n  instance.interceptors.response.use(\r\n    (response) => {\r\n      return response;\r\n    },\r\n    async (error: AxiosError) => {\r\n      const originalRequest = error.config as AxiosError['config'] & {\r\n        _retry?: boolean;\r\n        _retryCount?: number;\r\n      };\r\n\r\n      // Handle 429 Rate Limiting (same as customer API client)\r\n      if (error.response?.status === 429) {\r\n        // Ensure originalRequest exists and has the required properties\r\n        if (originalRequest && !originalRequest._retry) {\r\n          originalRequest._retry = true;\r\n\r\n          // Get retry delay from headers or use exponential backoff\r\n          const retryAfter = error.response.headers['retry-after'];\r\n          const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.min(1000 * Math.pow(2, originalRequest._retryCount || 0), 10000);\r\n\r\n          originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;\r\n\r\n          // Don't retry more than 3 times\r\n          if (originalRequest._retryCount <= 10) {\r\n            if (process.env.NODE_ENV === 'development') {\r\n              console.warn(`[Staff API] Rate limited. Retrying after ${delay}ms (attempt ${originalRequest._retryCount})`);\r\n            }\r\n\r\n            await new Promise(resolve => setTimeout(resolve, delay));\r\n            return instance(originalRequest);\r\n          } else {\r\n            // Exhausted retries\r\n            if (process.env.NODE_ENV === 'development') {\r\n              console.error('[Staff API] Rate limiting exhausted retries:', {\r\n                url: error.config?.url,\r\n                method: error.config?.method,\r\n                retryCount: originalRequest._retryCount\r\n              });\r\n            }\r\n          }\r\n        } else {\r\n          // Already retrying or no original request\r\n          if (process.env.NODE_ENV === 'development') {\r\n            console.warn('[Staff API] Rate limited but already retrying or no original request');\r\n          }\r\n        }\r\n      }\r\n\r\n      // Only log detailed errors in development (for non-rate-limiting errors or exhausted retries)\r\n      if (process.env.NODE_ENV === 'development' && error.response?.status !== 429) {\r\n        console.error('API Error Details:', {\r\n          url: error.config?.url,\r\n          method: error.config?.method,\r\n          status: error.response?.status,\r\n          statusText: error.response?.statusText,\r\n          message: error.message,\r\n          code: error.code,\r\n          data: error.response?.data || 'No response data',\r\n          headers: error.response?.headers || 'No response headers',\r\n          stack: error.stack\r\n        });\r\n      }\r\n      \r\n      // Log network errors separately for better debugging\r\n      if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {\r\n        console.error('Network Error Details:', {\r\n          baseURL: error.config?.baseURL,\r\n          url: error.config?.url,\r\n          method: error.config?.method,\r\n          timeout: error.config?.timeout,\r\n          message: error.message,\r\n          code: error.code\r\n        });\r\n      }\r\n\r\n      // Handle authentication errors - auto logout on 401\r\n      if (error.response?.status === 401) {\r\n        throw error;\r\n        \r\n      }\r\n\r\n      // Handle authorization errors\r\n      if (error.response?.status === 403) {\r\n        console.warn('Access denied - insufficient permissions');\r\n        // You can add a toast notification here\r\n      }\r\n\r\n      // Handle validation/conflict errors (duplicate registration, etc.)\r\n      if (error.response?.status === 409 || error.response?.status === 422) {\r\n        const errorData = error.response?.data as { message?: string };\r\n        console.warn('Validation/Conflict error:', errorData?.message || error.message);\r\n        // Let the calling service handle the specific error message\r\n      }\r\n\r\n      // Handle network errors\r\n      if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {\r\n        console.error('Network error - backend may not be accessible');\r\n        // You can add a toast notification here\r\n      }\r\n\r\n      // Handle timeout errors\r\n      if (error.code === 'ECONNABORTED') {\r\n        console.error('Request timeout - server took too long to respond');\r\n        // You can add a toast notification here\r\n      }\r\n\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n\r\n  return instance;\r\n};\r\n\r\n/**\r\n * Default API client instance\r\n */\r\nexport const apiClient = createApiClient();\r\n\r\n/**\r\n * Auth-specific API client\r\n */\r\nexport const authApiClient = createApiClient(`${API_BASE_URL}/auth`);\r\n\r\n/**\r\n * Users API client\r\n */\r\nexport const usersApiClient = createApiClient(`${API_BASE_URL}/users`);\r\n\r\n/**\r\n * Roles API client\r\n */\r\nexport const rolesApiClient = createApiClient(`${API_BASE_URL}/roles`);\r\n\r\n/**\r\n * Audit Trail API client\r\n */\r\nexport const auditApiClient = createApiClient(`${API_BASE_URL}/audit-trail`);\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAIA,MAAM,eAAe,6DAAmC;AAMjD,MAAM,kBAAkB,CAAC,UAAkB,YAAY;IAC5D,MAAM,WAAW,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;QAC5B;QACA,SAAS;QACT,SAAS;YACP,gBAAgB;QAClB;IACF;IAEA,0DAA0D;IAC1D,SAAS,YAAY,CAAC,OAAO,CAAC,GAAG,CAC/B,OAAO;QACL,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,OAAO;YACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;QAClD;QACA,kDAAkD;QAClD,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,OAAO,MAAM,EAAE,cAAc,aAAa,EAAE,OAAO,OAAO,GAAG,OAAO,GAAG,EAAE;QAC7G;QACA,OAAO;IACT,GACA,CAAC;QACC,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,QAAQ,MAAM,CAAC;IACxB;IAGF,0DAA0D;IAC1D,SAAS,YAAY,CAAC,QAAQ,CAAC,GAAG,CAChC,CAAC;QACC,OAAO;IACT,GACA,OAAO;QACL,MAAM,kBAAkB,MAAM,MAAM;QAKpC,yDAAyD;QACzD,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;YAClC,gEAAgE;YAChE,IAAI,mBAAmB,CAAC,gBAAgB,MAAM,EAAE;gBAC9C,gBAAgB,MAAM,GAAG;gBAEzB,0DAA0D;gBAC1D,MAAM,aAAa,MAAM,QAAQ,CAAC,OAAO,CAAC,cAAc;gBACxD,MAAM,QAAQ,aAAa,SAAS,cAAc,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,GAAG,gBAAgB,WAAW,IAAI,IAAI;gBAExH,gBAAgB,WAAW,GAAG,CAAC,gBAAgB,WAAW,IAAI,CAAC,IAAI;gBAEnE,gCAAgC;gBAChC,IAAI,gBAAgB,WAAW,IAAI,IAAI;oBACrC,wCAA4C;wBAC1C,QAAQ,IAAI,CAAC,CAAC,yCAAyC,EAAE,MAAM,YAAY,EAAE,gBAAgB,WAAW,CAAC,CAAC,CAAC;oBAC7G;oBAEA,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD,OAAO,SAAS;gBAClB,OAAO;oBACL,oBAAoB;oBACpB,wCAA4C;wBAC1C,QAAQ,KAAK,CAAC,gDAAgD;4BAC5D,KAAK,MAAM,MAAM,EAAE;4BACnB,QAAQ,MAAM,MAAM,EAAE;4BACtB,YAAY,gBAAgB,WAAW;wBACzC;oBACF;gBACF;YACF,OAAO;gBACL,0CAA0C;gBAC1C,wCAA4C;oBAC1C,QAAQ,IAAI,CAAC;gBACf;YACF;QACF;QAEA,8FAA8F;QAC9F,IAAI,oDAAyB,iBAAiB,MAAM,QAAQ,EAAE,WAAW,KAAK;YAC5E,QAAQ,KAAK,CAAC,sBAAsB;gBAClC,KAAK,MAAM,MAAM,EAAE;gBACnB,QAAQ,MAAM,MAAM,EAAE;gBACtB,QAAQ,MAAM,QAAQ,EAAE;gBACxB,YAAY,MAAM,QAAQ,EAAE;gBAC5B,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI;gBAChB,MAAM,MAAM,QAAQ,EAAE,QAAQ;gBAC9B,SAAS,MAAM,QAAQ,EAAE,WAAW;gBACpC,OAAO,MAAM,KAAK;YACpB;QACF;QAEA,qDAAqD;QACrD,IAAI,MAAM,IAAI,KAAK,iBAAiB,MAAM,OAAO,KAAK,iBAAiB;YACrE,QAAQ,KAAK,CAAC,0BAA0B;gBACtC,SAAS,MAAM,MAAM,EAAE;gBACvB,KAAK,MAAM,MAAM,EAAE;gBACnB,QAAQ,MAAM,MAAM,EAAE;gBACtB,SAAS,MAAM,MAAM,EAAE;gBACvB,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI;YAClB;QACF;QAEA,oDAAoD;QACpD,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;YAClC,MAAM;QAER;QAEA,8BAA8B;QAC9B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;YAClC,QAAQ,IAAI,CAAC;QACb,wCAAwC;QAC1C;QAEA,mEAAmE;QACnE,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,MAAM,QAAQ,EAAE,WAAW,KAAK;YACpE,MAAM,YAAY,MAAM,QAAQ,EAAE;YAClC,QAAQ,IAAI,CAAC,8BAA8B,WAAW,WAAW,MAAM,OAAO;QAC9E,4DAA4D;QAC9D;QAEA,wBAAwB;QACxB,IAAI,MAAM,IAAI,KAAK,iBAAiB,MAAM,OAAO,KAAK,iBAAiB;YACrE,QAAQ,KAAK,CAAC;QACd,wCAAwC;QAC1C;QAEA,wBAAwB;QACxB,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,QAAQ,KAAK,CAAC;QACd,wCAAwC;QAC1C;QAEA,OAAO,QAAQ,MAAM,CAAC;IACxB;IAGF,OAAO;AACT;AAKO,MAAM,YAAY;AAKlB,MAAM,gBAAgB,gBAAgB,GAAG,aAAa,KAAK,CAAC;AAK5D,MAAM,iBAAiB,gBAAgB,GAAG,aAAa,MAAM,CAAC;AAK9D,MAAM,iBAAiB,gBAAgB,GAAG,aAAa,MAAM,CAAC;AAK9D,MAAM,iBAAiB,gBAAgB,GAAG,aAAa,YAAY,CAAC", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/lib/authUtils.ts"], "sourcesContent": ["import Cookies from 'js-cookie';\r\nimport { getAuthToken, removeAuthToken } from './auth';\r\n\r\n/**\r\n * Utility functions for authentication management\r\n */\r\n\r\n/**\r\n * Check if a JWT token is expired\r\n * @param token - JWT token to check\r\n * @returns true if token is expired, false otherwise\r\n */\r\nexport const isTokenExpired = (token: string): boolean => {\r\n  if (!token) return true;\r\n\r\n  try {\r\n    // Decode JWT payload (without verification - just for expiry check)\r\n    const payload = JSON.parse(atob(token.split('.')[1]));\r\n    const currentTime = Math.floor(Date.now() / 1000);\r\n    \r\n    // Check if token has expired\r\n    return payload.exp < currentTime;\r\n  } catch (error) {\r\n    console.error('Error decoding token:', error);\r\n    return true; // Treat invalid tokens as expired\r\n  }\r\n};\r\n\r\n/**\r\n * Validate current authentication state\r\n * @returns true if user is properly authenticated, false otherwise\r\n */\r\nexport const validateAuthState = (): boolean => {\r\n  const token = getAuthToken();\r\n  const userCookie = Cookies.get('auth_user');\r\n\r\n  // Check if token exists and is not expired\r\n  if (!token || isTokenExpired(token)) {\r\n    console.warn('Token is missing or expired');\r\n    return false;\r\n  }\r\n\r\n  // Check if user data exists\r\n  if (!userCookie) {\r\n    console.warn('User data is missing');\r\n    return false;\r\n  }\r\n\r\n  try {\r\n    JSON.parse(userCookie); // Validate user data format\r\n    return true;\r\n  } catch (error) {\r\n    console.error('Invalid user data format:', error);\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Clear all authentication data and redirect to login\r\n */\r\nexport const forceLogout = (): void => {\r\n  console.warn('Forcing logout due to invalid authentication state');\r\n  \r\n  // Clear all auth data\r\n  removeAuthToken();\r\n  Cookies.remove('auth_token');\r\n  Cookies.remove('auth_user');\r\n  \r\n  // Clear localStorage and sessionStorage\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.removeItem('auth_token');\r\n    localStorage.removeItem('user_preferences');\r\n    sessionStorage.clear();\r\n    \r\n    // Redirect to appropriate login based on current portal\r\n    const isCustomerPortal = window.location.pathname.startsWith('/customer') || \r\n                            window.location.hostname.includes('customer');\r\n    \r\n    window.location.href = isCustomerPortal ? '/customer/auth/login' : '/auth/login';\r\n  }\r\n};\r\n\r\n/**\r\n * Periodically check token validity and auto-logout if expired\r\n * @param intervalMs - Check interval in milliseconds (default: 60 seconds)\r\n */\r\nexport const startTokenValidationTimer = (intervalMs: number = 60000): NodeJS.Timeout => {\r\n  return setInterval(() => {\r\n    if (!validateAuthState()) {\r\n      forceLogout();\r\n    }\r\n  }, intervalMs);\r\n};\r\n\r\n/**\r\n * Periodically check token validity and auto-logout if expired\r\n * @param intervalMs - Check interval in milliseconds (default: 60 seconds)\r\n */\r\nexport const processApiResponse = (response: any): any =>  {\r\n\r\n    // Check if it's a standard datatable success response format\r\n  if (response?.data?.meta !== undefined && response.data.data) {\r\n    return response.data;\r\n  }\r\n\r\n  // Check if it's a standard success response format with message at top level\r\n  if (response?.data?.data && response?.data?.message) {\r\n    // Combine the message from top level with the data from the data property\r\n    return {\r\n      ...response.data.data,\r\n      message: response.data.message\r\n    };\r\n  }\r\n\r\n  // Check if it's a standard success response format without message\r\n  if (response?.data?.data) {\r\n    return response.data.data;\r\n  }\r\n\r\n  // Check if it's direct data format\r\n  else if (response.data) {\r\n    return response.data;\r\n  }\r\n\r\n  return response.data;\r\n};\r\n\r\n/**\r\n * Check token expiry and warn user before it expires\r\n * @param warningMinutes - Minutes before expiry to show warning (default: 5)\r\n */\r\nexport const checkTokenExpiry = (warningMinutes: number = 5): void => {\r\n  const token = getAuthToken();\r\n  if (!token) return;\r\n\r\n  try {\r\n    const payload = JSON.parse(atob(token.split('.')[1]));\r\n    const expiryTime = payload.exp * 1000; // Convert to milliseconds\r\n    const currentTime = Date.now();\r\n    const timeUntilExpiry = expiryTime - currentTime;\r\n    const warningTime = warningMinutes * 60 * 1000; // Convert to milliseconds\r\n\r\n    if (timeUntilExpiry <= warningTime && timeUntilExpiry > 0) {\r\n      console.warn(`Token will expire in ${Math.floor(timeUntilExpiry / 60000)} minutes`);\r\n      // You can add a toast notification here if needed\r\n    }\r\n  } catch (error) {\r\n    console.error('Error checking token expiry:', error);\r\n  }\r\n};\r\n\r\n\r\n\r\nexport const getErrorMessage = (err: unknown): string => {\r\n  // Type guard to check if error has expected properties\r\n  const isAxiosError = (error: unknown): error is {\r\n    code?: string;\r\n    message?: string;\r\n    response?: {\r\n      data?: {\r\n        message?: string | string[];\r\n        statusCode?: number;\r\n        error?: string;\r\n      };\r\n    };\r\n  } => {\r\n    return typeof error === 'object' && error !== null;\r\n  };\r\n\r\n  if (!isAxiosError(err)) {\r\n    return 'An unexpected error occurred. Please try again.';\r\n  }\r\n\r\n  // Handle network errors\r\n  if (err.code === 'ERR_NETWORK' || err.message === 'Network Error') {\r\n    return 'Unable to connect to the server. Please check your internet connection and try again.';\r\n  }\r\n\r\n  // Handle timeout errors\r\n  if (err.code === 'ECONNABORTED') {\r\n    return 'Request timed out. Please check your connection and try again.';\r\n  }\r\n\r\n  // Handle response errors\r\n  if (err.response?.data) {\r\n    const { message, statusCode } = err.response.data;\r\n\r\n    // Handle validation errors (array of messages)\r\n    if (Array.isArray(message)) {\r\n      return message.join('. ');\r\n    }\r\n\r\n    // Handle specific status codes\r\n    switch (statusCode) {\r\n      case 400:\r\n        if (typeof message === 'string') {\r\n          return message;\r\n        }\r\n        return 'Invalid input. Please check your email and password format.';\r\n\r\n      case 401:\r\n        if (message && message.toLowerCase().includes('invalid email')) {\r\n          return message;\r\n        }\r\n        if (message && message.toLowerCase().includes('account')) {\r\n          return 'Account not found or inactive. Please contact support if you believe this is an error.';\r\n        }\r\n        return 'Authentication failed. Please verify your email and password.';\r\n\r\n      case 403:\r\n        return 'Your account has been suspended or you do not have permission to access this system.';\r\n\r\n      case 429:\r\n        return 'Too many login attempts. Please wait a few minutes before trying again.';\r\n\r\n      case 500:\r\n        return 'Server error occurred. Please try again later or contact support.';\r\n\r\n      case 503:\r\n        return 'Service temporarily unavailable. Please try again in a few minutes.';\r\n\r\n      default:\r\n        if (typeof message === 'string') {\r\n          return message;\r\n        }\r\n        return `Login failed (Error ${statusCode}). Please try again.`;\r\n    }\r\n  }\r\n\r\n  // Handle other error types\r\n  if (err.message) {\r\n    // Handle specific error messages\r\n    if (err.message.toLowerCase().includes('fetch')) {\r\n      return 'Unable to connect to the server. Please check your internet connection.';\r\n    }\r\n    return err.message;\r\n  }\r\n\r\n  return 'An unexpected error occurred. Please try again.';\r\n};"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAWO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI;QACF,oEAAoE;QACpE,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QAE5C,6BAA6B;QAC7B,OAAO,QAAQ,GAAG,GAAG;IACvB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,MAAM,kCAAkC;IACjD;AACF;AAMO,MAAM,oBAAoB;IAC/B,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IACzB,MAAM,aAAa,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;IAE/B,2CAA2C;IAC3C,IAAI,CAAC,SAAS,eAAe,QAAQ;QACnC,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,4BAA4B;IAC5B,IAAI,CAAC,YAAY;QACf,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,IAAI;QACF,KAAK,KAAK,CAAC,aAAa,4BAA4B;QACpD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAKO,MAAM,cAAc;IACzB,QAAQ,IAAI,CAAC;IAEb,sBAAsB;IACtB,CAAA,GAAA,kHAAA,CAAA,kBAAe,AAAD;IACd,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IACf,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IAEf,wCAAwC;IACxC;;AAWF;AAMO,MAAM,4BAA4B,CAAC,aAAqB,KAAK;IAClE,OAAO,YAAY;QACjB,IAAI,CAAC,qBAAqB;YACxB;QACF;IACF,GAAG;AACL;AAMO,MAAM,qBAAqB,CAAC;IAE/B,6DAA6D;IAC/D,IAAI,UAAU,MAAM,SAAS,aAAa,SAAS,IAAI,CAAC,IAAI,EAAE;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,6EAA6E;IAC7E,IAAI,UAAU,MAAM,QAAQ,UAAU,MAAM,SAAS;QACnD,0EAA0E;QAC1E,OAAO;YACL,GAAG,SAAS,IAAI,CAAC,IAAI;YACrB,SAAS,SAAS,IAAI,CAAC,OAAO;QAChC;IACF;IAEA,mEAAmE;IACnE,IAAI,UAAU,MAAM,MAAM;QACxB,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B,OAGK,IAAI,SAAS,IAAI,EAAE;QACtB,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO,SAAS,IAAI;AACtB;AAMO,MAAM,mBAAmB,CAAC,iBAAyB,CAAC;IACzD,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IACzB,IAAI,CAAC,OAAO;IAEZ,IAAI;QACF,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD,MAAM,aAAa,QAAQ,GAAG,GAAG,MAAM,0BAA0B;QACjE,MAAM,cAAc,KAAK,GAAG;QAC5B,MAAM,kBAAkB,aAAa;QACrC,MAAM,cAAc,iBAAiB,KAAK,MAAM,0BAA0B;QAE1E,IAAI,mBAAmB,eAAe,kBAAkB,GAAG;YACzD,QAAQ,IAAI,CAAC,CAAC,qBAAqB,EAAE,KAAK,KAAK,CAAC,kBAAkB,OAAO,QAAQ,CAAC;QAClF,kDAAkD;QACpD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;IAChD;AACF;AAIO,MAAM,kBAAkB,CAAC;IAC9B,uDAAuD;IACvD,MAAM,eAAe,CAAC;QAWpB,OAAO,OAAO,UAAU,YAAY,UAAU;IAChD;IAEA,IAAI,CAAC,aAAa,MAAM;QACtB,OAAO;IACT;IAEA,wBAAwB;IACxB,IAAI,IAAI,IAAI,KAAK,iBAAiB,IAAI,OAAO,KAAK,iBAAiB;QACjE,OAAO;IACT;IAEA,wBAAwB;IACxB,IAAI,IAAI,IAAI,KAAK,gBAAgB;QAC/B,OAAO;IACT;IAEA,yBAAyB;IACzB,IAAI,IAAI,QAAQ,EAAE,MAAM;QACtB,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,IAAI,QAAQ,CAAC,IAAI;QAEjD,+CAA+C;QAC/C,IAAI,MAAM,OAAO,CAAC,UAAU;YAC1B,OAAO,QAAQ,IAAI,CAAC;QACtB;QAEA,+BAA+B;QAC/B,OAAQ;YACN,KAAK;gBACH,IAAI,OAAO,YAAY,UAAU;oBAC/B,OAAO;gBACT;gBACA,OAAO;YAET,KAAK;gBACH,IAAI,WAAW,QAAQ,WAAW,GAAG,QAAQ,CAAC,kBAAkB;oBAC9D,OAAO;gBACT;gBACA,IAAI,WAAW,QAAQ,WAAW,GAAG,QAAQ,CAAC,YAAY;oBACxD,OAAO;gBACT;gBACA,OAAO;YAET,KAAK;gBACH,OAAO;YAET,KAAK;gBACH,OAAO;YAET,KAAK;gBACH,OAAO;YAET,KAAK;gBACH,OAAO;YAET;gBACE,IAAI,OAAO,YAAY,UAAU;oBAC/B,OAAO;gBACT;gBACA,OAAO,CAAC,oBAAoB,EAAE,WAAW,oBAAoB,CAAC;QAClE;IACF;IAEA,2BAA2B;IAC3B,IAAI,IAAI,OAAO,EAAE;QACf,iCAAiC;QACjC,IAAI,IAAI,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU;YAC/C,OAAO;QACT;QACA,OAAO,IAAI,OAAO;IACpB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/auth.service.ts"], "sourcesContent": ["import { AxiosInstance } from 'axios';\r\nimport { authApiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { AuthResponse, ForgotPasswordData, LoginData, RegisterData, ResetPasswordData, SetupTwoFactorAuthResponse, SetUpTwoFactorData, TwoFactorData, TwoFactorResponse } from '@/types';\r\n\r\n\r\nclass AuthService {\r\n  private api: AxiosInstance;\r\n\r\n  constructor() {\r\n    // Use the centralized auth API client with proper error handling\r\n    this.api = authApiClient;\r\n  }\r\n\r\n  async login(data: LoginData): Promise<AuthResponse> {\r\n    const response = await this.api.post('/login', data);\r\n\r\n    // The backend returns data directly, not nested in a data property\r\n    const authData = processApiResponse(response);\r\n\r\n    // Check if the auth data is empty (which indicates an error)\r\n    if (!authData || Object.keys(authData).length === 0) {\r\n      throw new Error('Authentication failed - invalid credentials');\r\n    }\r\n\r\n    // Validate that we have the required fields\r\n    // For account recovery, we might not have access_token but should have user info\r\n    if (!authData.user) {\r\n      throw new Error('Authentication failed - incomplete response: missing user data');\r\n    }\r\n\r\n    // For normal login, we need access_token unless it's a recovery scenario or 2FA is required\r\n    if (!authData.access_token && !authData.requiresRecovery && !authData.requiresTwoFactor) {\r\n      throw new Error('Authentication failed - incomplete response: missing access token');\r\n    }\r\n\r\n    return authData;\r\n  }\r\n\r\n  async register(data: RegisterData): Promise<AuthResponse> {\r\n    const response = await this.api.post('/register', data);\r\n\r\n    // The backend returns data directly, not nested in a data property\r\n    return response.data;\r\n  }\r\n\r\n  async forgotPassword(data: ForgotPasswordData): Promise<{ message: string }> {\r\n    const response = await this.api.post('/forgot-password', data);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async resetPassword(data: ResetPasswordData): Promise<{ message: string }> {\r\n    try {\r\n      console.log('🔄 Calling reset password API:', { ...data, new_password: '***' });\r\n      const response = await this.api.post('/reset-password', data);\r\n      console.log('✅ Reset password API response:', response.data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('❌ Reset password API error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async verify2FA(data: TwoFactorData): Promise<TwoFactorResponse> {\r\n    try {\r\n      console.log('🔄 Calling verify 2FA API:', data);\r\n      const response = await this.api.post('/verify-2fa', data);\r\n      console.log('✅ Verify 2FA API response:', response.data);\r\n\r\n      // Process the API response to extract the actual data and preserve message\r\n      const processedResponse = processApiResponse(response);\r\n      console.log('✅ Processed verify 2FA response:', processedResponse);\r\n\r\n      return processedResponse;\r\n    } catch (error) {\r\n      console.error('❌ Verify 2FA API error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async verifyEmail(data: TwoFactorData): Promise<TwoFactorResponse> {\r\n    try {\r\n      console.log('🔄 Calling verify email API:', data);\r\n      // Use GET request with query parameters to match backend route\r\n      const response = await this.api.get('/verify-email', {\r\n        params: {\r\n          i: data.user_id,\r\n          unique: data.unique,\r\n          c: data.code\r\n        }\r\n      });\r\n      console.log('✅ Verify email API response:', response.data);\r\n\r\n      // Process the API response to extract the actual data and preserve message\r\n      const processedResponse = processApiResponse(response);\r\n      console.log('✅ Processed verify email response:', processedResponse);\r\n\r\n      return processedResponse;\r\n    } catch (error) {\r\n      console.error('❌ Verify email API error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async setupTwoFactorAuth(data: SetUpTwoFactorData): Promise<SetupTwoFactorAuthResponse> {\r\n    // Debug: Check multiple token sources\r\n    const localStorageToken = this.getAuthToken();\r\n    const cookieToken = typeof window !== 'undefined' ?\r\n      document.cookie.split('; ').find(row => row.startsWith('auth_token='))?.split('=')[1] : null;\r\n\r\n    console.log('🔐 SetupTwoFactorAuth - localStorage token exists:', !!localStorageToken);\r\n    console.log('🔐 SetupTwoFactorAuth - cookie token exists:', !!cookieToken);\r\n    console.log('🔐 SetupTwoFactorAuth - localStorage token preview:', localStorageToken ? localStorageToken.substring(0, 20) + '...' : 'No token');\r\n    console.log('🔐 SetupTwoFactorAuth - cookie token preview:', cookieToken ? cookieToken.substring(0, 20) + '...' : 'No token');\r\n\r\n    // Use the most reliable token source\r\n    const token = localStorageToken || cookieToken;\r\n\r\n    if (!token) {\r\n      throw new Error('No authentication token found. Please login again.');\r\n    }\r\n\r\n    // Validate token format (basic JWT check)\r\n    const tokenParts = token.split('.');\r\n    if (tokenParts.length !== 3) {\r\n      console.error('🔐 Invalid JWT format - expected 3 parts, got:', tokenParts.length);\r\n      throw new Error('Invalid authentication token format. Please login again.');\r\n    }\r\n\r\n    // Ensure token is set in the request headers\r\n    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n\r\n    try {\r\n      const response = await this.api.post('/setup-2fa', data);\r\n      return response.data.data; // Its wrapped in another data object\r\n    } catch (error: any) {\r\n      console.error('🔐 SetupTwoFactorAuth API Error:', error.response?.data || error.message);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n\r\n  async verifyTwoFactorCode(data: TwoFactorData): Promise<{ message: string }> {\r\n    const response = await this.api.post('/verify-2fa', data);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async generateTwoFactorCode(userId: string, action: string): Promise<{ message: string }> {\r\n    const response = await this.api.post('/generate-2fa', { user_id: userId, action });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async refreshToken(): Promise<AuthResponse> {\r\n    const response = await this.api.post('/refresh');\r\n    return response.data;\r\n  }\r\n\r\n  setAuthToken(token: string): void {\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.setItem('auth_token', token);\r\n    }\r\n  }\r\n\r\n  getAuthToken(): string | null {\r\n    if (typeof window !== 'undefined') {\r\n      // Try localStorage first\r\n      const localToken = localStorage.getItem('auth_token');\r\n      if (localToken) return localToken;\r\n\r\n      // Fallback to cookies\r\n      const cookieToken = document.cookie\r\n        .split('; ')\r\n        .find(row => row.startsWith('auth_token='))\r\n        ?.split('=')[1];\r\n\r\n      return cookieToken || null;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  private getCookieValue(name: string): string | null {\r\n    if (typeof window === 'undefined') return null;\r\n\r\n    const value = `; ${document.cookie}`;\r\n    const parts = value.split(`; ${name}=`);\r\n    if (parts.length === 2) {\r\n      return parts.pop()?.split(';').shift() || null;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  clearAuthToken(): void {\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.removeItem('auth_token');\r\n    }\r\n  }\r\n}\r\n\r\nexport const authService = new AuthService();\r\n"], "names": [], "mappings": ";;;AACA;AACA;;;AAIA,MAAM;IACI,IAAmB;IAE3B,aAAc;QACZ,iEAAiE;QACjE,IAAI,CAAC,GAAG,GAAG,uHAAA,CAAA,gBAAa;IAC1B;IAEA,MAAM,MAAM,IAAe,EAAyB;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU;QAE/C,mEAAmE;QACnE,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAEpC,6DAA6D;QAC7D,IAAI,CAAC,YAAY,OAAO,IAAI,CAAC,UAAU,MAAM,KAAK,GAAG;YACnD,MAAM,IAAI,MAAM;QAClB;QAEA,4CAA4C;QAC5C,iFAAiF;QACjF,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,4FAA4F;QAC5F,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,SAAS,gBAAgB,IAAI,CAAC,SAAS,iBAAiB,EAAE;YACvF,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT;IAEA,MAAM,SAAS,IAAkB,EAAyB;QACxD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa;QAElD,mEAAmE;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,eAAe,IAAwB,EAAgC;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB;QACzD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,IAAuB,EAAgC;QACzE,IAAI;YACF,QAAQ,GAAG,CAAC,kCAAkC;gBAAE,GAAG,IAAI;gBAAE,cAAc;YAAM;YAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB;YACxD,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;YAC3D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,MAAM,UAAU,IAAmB,EAA8B;QAC/D,IAAI;YACF,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe;YACpD,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;YAEvD,2EAA2E;YAC3E,MAAM,oBAAoB,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAC7C,QAAQ,GAAG,CAAC,oCAAoC;YAEhD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,MAAM,YAAY,IAAmB,EAA8B;QACjE,IAAI;YACF,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,+DAA+D;YAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB;gBACnD,QAAQ;oBACN,GAAG,KAAK,OAAO;oBACf,QAAQ,KAAK,MAAM;oBACnB,GAAG,KAAK,IAAI;gBACd;YACF;YACA,QAAQ,GAAG,CAAC,gCAAgC,SAAS,IAAI;YAEzD,2EAA2E;YAC3E,MAAM,oBAAoB,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAC7C,QAAQ,GAAG,CAAC,sCAAsC;YAElD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB,IAAwB,EAAuC;QACtF,sCAAsC;QACtC,MAAM,oBAAoB,IAAI,CAAC,YAAY;QAC3C,MAAM,cAAc,sCAClB,0BAAwF;QAE1F,QAAQ,GAAG,CAAC,sDAAsD,CAAC,CAAC;QACpE,QAAQ,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAC9D,QAAQ,GAAG,CAAC,uDAAuD,oBAAoB,kBAAkB,SAAS,CAAC,GAAG,MAAM,QAAQ;QACpI,QAAQ,GAAG,CAAC,iDAAiD,sCAAc,0BAAuC;QAElH,qCAAqC;QACrC,MAAM,QAAQ,qBAAqB;QAEnC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,0CAA0C;QAC1C,MAAM,aAAa,MAAM,KAAK,CAAC;QAC/B,IAAI,WAAW,MAAM,KAAK,GAAG;YAC3B,QAAQ,KAAK,CAAC,kDAAkD,WAAW,MAAM;YACjF,MAAM,IAAI,MAAM;QAClB;QAEA,6CAA6C;QAC7C,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;QAErE,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc;YACnD,OAAO,SAAS,IAAI,CAAC,IAAI,EAAE,qCAAqC;QAClE,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,oCAAoC,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YACvF,MAAM;QACR;IACF;IAGA,MAAM,oBAAoB,IAAmB,EAAgC;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe;QACpD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,sBAAsB,MAAc,EAAE,MAAc,EAAgC;QACxF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB;YAAE,SAAS;YAAQ;QAAO;QAChF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAsC;QAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,KAAa,EAAQ;QAChC;;IAGF;IAEA,eAA8B;QAC5B;;QAaA,OAAO;IACT;IAEQ,eAAe,IAAY,EAAiB;QAClD,wCAAmC,OAAO;;;QAE1C,MAAM;QACN,MAAM;IAKR;IAEA,iBAAuB;QACrB;;IAGF;AACF;AAEO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/lib/customer-api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';\r\nimport Cookies from 'js-cookie';\r\nimport { processApiResponse } from './authUtils';\r\n\r\nimport { ComplaintData, DeactivateAccountData, LicenseApplicationData, PaymentCreateData, ProfileUpdateData, TenderPaymentData } from '@/types/payment';\r\nimport { SearchPostcodes, CreateAddressDto as CreateAddressData } from '@/types';\r\nimport { EditAddressData, PostalCodeLookupResult } from '@/types/address_types';\r\n\r\n\r\n// API Configuration\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n\r\n// Create axios instance for customer portal (same as staff portal)\r\nconst customerApiClient: AxiosInstance = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  timeout: 120000, // Increased timeout to match main API client (120 seconds)\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json',\r\n  },\r\n});\r\n\r\n// Create auth-specific client (same as staff portal)\r\nconst customerAuthApiClient: AxiosInstance = axios.create({\r\n  baseURL: `${API_BASE_URL}/auth`,\r\n  timeout: 120000, // Increased timeout to match main API client\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json',\r\n  },\r\n});\r\n\r\ncustomerAuthApiClient.interceptors.response.use(\r\n  (response) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Customer Auth API Response Success:', {\r\n        status: response.status,\r\n        statusText: response.statusText,\r\n        url: response.config.url\r\n      });\r\n    }\r\n    return response;\r\n  },\r\n  (error) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.error('Customer Auth API Interceptor Error:', {\r\n        message: error?.message || 'Unknown error',\r\n        code: error?.code || 'NO_CODE',\r\n        status: error?.response?.status || 'NO_STATUS',\r\n        statusText: error?.response?.statusText || 'NO_STATUS_TEXT',\r\n        url: error?.config?.url || 'NO_URL',\r\n        method: error?.config?.method || 'NO_METHOD',\r\n        baseURL: error?.config?.baseURL || 'NO_BASE_URL',\r\n        isAxiosError: error?.isAxiosError || false,\r\n        responseData: error?.response?.data || 'NO_RESPONSE_DATA',\r\n        requestData: error?.config?.data || 'NO_REQUEST_DATA',\r\n        headers: error?.config?.headers || 'NO_HEADERS'\r\n      });\r\n    }\r\n\r\n    // Don't handle 401 here, let the login method handle it\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Request interceptor to add auth token\r\ncustomerApiClient.interceptors.request.use(\r\n  (config) => {\r\n    const token = Cookies.get('auth_token');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor for error handling with retry logic\r\ncustomerApiClient.interceptors.response.use(\r\n  (response: AxiosResponse) => {\r\n    return response;\r\n  },\r\n  async (error: AxiosError) => {\r\n    const originalRequest = error.config as AxiosError['config'] & {\r\n      _retry?: boolean;\r\n      _retryCount?: number;\r\n    };\r\n\r\n    // Handle 429 Rate Limiting\r\n    if (error.response?.status === 429) {\r\n      if (!originalRequest._retry) {\r\n        originalRequest._retry = true;\r\n        \r\n        // Get retry delay from headers or use exponential backoff\r\n        const retryAfter = error.response.headers['retry-after'];\r\n        const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.min(1000 * Math.pow(2, originalRequest._retryCount || 0), 10000);\r\n        \r\n        originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;\r\n        \r\n        // Don't retry more than 3 times\r\n        if (originalRequest._retryCount <= 3) {\r\n          console.warn(`Rate limited. Retrying after ${delay}ms (attempt ${originalRequest._retryCount})`);\r\n          \r\n          await new Promise(resolve => setTimeout(resolve, delay));\r\n          return customerApiClient(originalRequest);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Handle 401 Unauthorized\r\n    if (error.response?.status === 401) {\r\n      // Clear auth token and redirect to customer login\r\n      Cookies.remove('auth_token');\r\n      Cookies.remove('auth_user');\r\n      window.location.href = '/customer/auth/login';\r\n    }\r\n    \r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// API Service Class\r\nexport class CustomerApiService {\r\n  public api: AxiosInstance;\r\n  private pendingRequests: Map<string, Promise<unknown>> = new Map();\r\n\r\n  constructor() {\r\n    this.api = customerApiClient;\r\n  }\r\n\r\n  // Request deduplication helper\r\n  private async deduplicateRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {\r\n    if (this.pendingRequests.has(key)) {\r\n      return this.pendingRequests.get(key) as Promise<T>;\r\n    }\r\n\r\n    const promise = requestFn().finally(() => {\r\n      this.pendingRequests.delete(key);\r\n    });\r\n\r\n    this.pendingRequests.set(key, promise);\r\n    return promise;\r\n  }\r\n\r\n  // Set auth token\r\n  setAuthToken(token: string) {\r\n    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n  }\r\n\r\n  // Remove auth token\r\n  removeAuthToken() {\r\n    delete this.api.defaults.headers.common['Authorization'];\r\n  }\r\n\r\n  async logout() {\r\n    const response = await customerAuthApiClient.post('/logout');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async refreshToken() {\r\n    const response = await customerAuthApiClient.post('/refresh');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // 2FA endpoints\r\n  async generateTwoFactorCode(userId: string, action: string) {\r\n    const response = await customerAuthApiClient.post('/generate-2fa', { user_id: userId, action });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async verify2FA(data: { user_id: string; code: string; unique: string }) {\r\n    const response = await customerAuthApiClient.post('/verify-2fa', data);\r\n\r\n    // Handle response structure consistently with login\r\n    if (processApiResponse(response)?.data) {\r\n      const authData = processApiResponse(response).data;\r\n      \r\n      // Map backend field names to frontend expected format\r\n      const mappedAuthData = {\r\n        access_token: authData.access_token,\r\n        user: {\r\n          id: authData.user.user_id,\r\n          firstName: authData.user.first_name,\r\n          lastName: authData.user.last_name,\r\n          email: authData.user.email,\r\n          roles: authData.user.roles || [],\r\n          isAdmin: (authData.user.roles || []).includes('administrator'),\r\n          profileImage: authData.user.profile_image,\r\n          createdAt: authData.user.created_at || new Date().toISOString(),\r\n          lastLogin: authData.user.last_login,\r\n          organizationName: authData.user.organization_name,\r\n          two_factor_enabled: authData.user.two_factor_enabled\r\n        }\r\n      };\r\n      \r\n      return mappedAuthData;\r\n    }\r\n\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // User profile endpoints\r\n  async getProfile() {\r\n    return this.deduplicateRequest('getProfile', async () => {\r\n      const response = await this.api.get('/users/profile');\r\n      return processApiResponse(response);\r\n    });\r\n  }\r\n\r\n  async updateProfile(profileData: ProfileUpdateData) {\r\n    const response = await this.api.put('/users/profile', profileData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async deactivateAccount(deactivationData: DeactivateAccountData) {\r\n    const response = await this.api.post('/users/deactivate', deactivationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Addressing endpoints\r\n  async getAddresses() {\r\n    const response = await this.api.get('/address/all');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createAddress(addressData: CreateAddressData) {\r\n    const response = await this.api.post('/address/create', addressData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getAddress(id: string) {\r\n    const response = await this.api.get(`/address/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async editAddress(addressData: EditAddressData) {\r\n    const { address_id, ...updateData } = addressData;\r\n    if (!address_id) {\r\n      throw new Error('Address ID is required for updating');\r\n    }\r\n    const response = await this.api.put(`/address/${address_id}`, updateData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getAddressesByEntity(entityType: string, entityId: string) {\r\n    const response = await this.api.get(`/address/all?entity_type=${encodeURIComponent(entityType)}&entity_id=${encodeURIComponent(entityId)}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async deleteAddress(id: string) {\r\n    const response = await this.api.delete(`/address/soft/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async searchPostcodes(searchParams: SearchPostcodes) {\r\n    const response = await this.api.post('/postal-codes/search', searchParams);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getAllPostcodes(params?: { page?: number; limit?: number }) {\r\n    // Ensure parameters are properly formatted as numbers\r\n    const cleanParams = params ? {\r\n      page: Number(params.page) || 1,\r\n      limit: Number(params.limit) || 1000\r\n    } : { page: 1, limit: 1000 };\r\n\r\n    const response = await this.api.get('/postal-codes/all', {\r\n      params: cleanParams\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getPostcodeByPostalCode(postalCode: string) : Promise<PostalCodeLookupResult> {\r\n    const response = await this.api.get(`/postal-codes/search/${postalCode}`);\r\n    return processApiResponse(response.data);\r\n  }\r\n\r\n  // License endpoints\r\n  async getLicenses(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/licenses', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicense(id: string) {\r\n    const response = await this.api.get(`/licenses/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createLicenseApplication(applicationData: LicenseApplicationData) {\r\n    const response = await this.api.post('/license-applications', applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License Types endpoints\r\n  async getLicenseTypes(params?: { page?: number; limit?: number }) {\r\n    const response = await this.api.get('/license-types', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseType(id: string) {\r\n    const response = await this.api.get(`/license-types/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // License Categories endpoints\r\n  async getLicenseCategories(params?: { page?: number; limit?: number }) {\r\n    const response = await this.api.get('/license-categories', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategoriesByType(licenseTypeId: string) {\r\n    const response = await this.api.get(`/license-categories/by-license-type/${licenseTypeId}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategoryTree(licenseTypeId: string) {\r\n    const response = await this.api.get(`/license-categories/license-type/${licenseTypeId}/tree`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getLicenseCategory(id: string) {\r\n    const response = await this.api.get(`/license-categories/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Application endpoints\r\n  async getApplications(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/applications', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getApplication(id: string) {\r\n    const response = await this.api.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createApplication(applicationData: any) {\r\n    const response = await this.api.post('/applications', applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async updateApplication(id: string, applicationData: Partial<LicenseApplicationData>) {\r\n    const response = await this.api.put(`/applications/${id}`, applicationData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Payment endpoints (customer-specific)\r\n  async getPayments(params?: { status?: string; page?: number; limit?: number; search?: string; paymentType?: string; dateRange?: string }) {\r\n    const response = await this.api.get('/payments/customer/my-payments', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getPayment(id: string) {\r\n    const response = await this.api.get(`/payments/customer/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async createPayment(paymentData: PaymentCreateData) {\r\n    const response = await this.api.post('/payments', paymentData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async uploadDocument(formData: FormData) {\r\n    const response = await this.api.post('/documents/upload', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async linkProofOfPayment(invoiceId: string, paymentData: any) {\r\n    const response = await this.api.post(`/payments/customer/invoice/${invoiceId}/proof-of-payment`, paymentData, {\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async uploadProofOfPayment(invoiceId: string, formData: FormData) {\r\n    try {\r\n      // Step 1: Upload the file to documents endpoint\r\n      console.log('📤 Step 1: Uploading proof of payment file to documents endpoint');\r\n\r\n      // Create a new FormData for the document upload with required fields\r\n      const documentFormData = new FormData();\r\n\r\n      // Copy the file from the original formData\r\n      const file = formData.get('file') as File;\r\n      if (!file) {\r\n        throw new Error('No file provided for upload');\r\n      }\r\n\r\n      documentFormData.append('file', file);\r\n      documentFormData.append('document_type', 'proof_of_payment');\r\n      documentFormData.append('entity_type', 'invoice');\r\n      documentFormData.append('entity_id', invoiceId);\r\n      documentFormData.append('file_name', file.name);\r\n\r\n      const uploadResponse = await this.api.post('/documents/upload', documentFormData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n      const uploadResult = processApiResponse(uploadResponse);\r\n\r\n      if (!uploadResult.success || !uploadResult.data?.document_id) {\r\n        throw new Error('Failed to upload document');\r\n      }\r\n\r\n      const documentId = uploadResult.data.document_id;\r\n      console.log('✅ Step 1 complete: Document uploaded with ID:', documentId);\r\n\r\n      // Step 2: Link the uploaded document to the payment\r\n      console.log('📤 Step 2: Linking document to payment');\r\n\r\n      // Extract additional data from formData\r\n      const paymentData: any = { documentId };\r\n\r\n      // Get other form data fields if they exist\r\n      for (const [key, value] of formData.entries()) {\r\n        if (key !== 'file' && key !== 'document_type' && key !== 'entity_type' && key !== 'entity_id') {\r\n          paymentData[key] = value;\r\n        }\r\n      }\r\n\r\n      const linkResponse = await this.api.post(`/payments/customer/invoice/${invoiceId}/proof-of-payment`, paymentData, {\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      console.log('✅ Step 2 complete: Document linked to payment');\r\n      return processApiResponse(linkResponse);\r\n\r\n    } catch (error) {\r\n      console.error('❌ Proof of payment upload failed:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getPaymentStatistics() {\r\n    const response = await this.api.get('/payments/customer/statistics');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getApplicationPayments(applicationId: string) {\r\n    const response = await this.api.get(`/payments/customer/application/${applicationId}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getInvoicePayments(invoiceId: string) {\r\n    const response = await this.api.get(`/payments/customer/invoice/${invoiceId}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Invoice endpoints (customer-specific)\r\n  async getInvoices(params?: { status?: string; page?: number; limit?: number; search?: string }) {\r\n    const response = await this.api.get('/customer-invoices', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getInvoice(id: string) {\r\n    const response = await this.api.get(`/customer-invoices/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getInvoicesByEntity(entityType: string, entityId: string) {\r\n    // For customer API, we use application-specific endpoint\r\n    if (entityType === 'application') {\r\n      const response = await this.api.get(`/customer-invoices/application/${entityId}`);\r\n      return processApiResponse(response);\r\n    }\r\n    // Fallback to general endpoint for other entity types\r\n    const response = await this.api.get(`/invoices/entity/${entityType}/${entityId}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getApplicationInvoiceStatus(applicationId: string) {\r\n    const response = await this.api.get(`/customer-invoices/application/${applicationId}/status`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getInvoiceStatistics() {\r\n    const response = await this.api.get('/customer-invoices/statistics');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async downloadInvoice(invoiceId: string): Promise<Blob> {\r\n    const response = await this.api.get(`/customer-invoices/${invoiceId}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return response.data;\r\n  }\r\n\r\n  // Document endpoints\r\n  async getDocuments(params?: any) {\r\n    const response = await this.api.get('/documents', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async uploadDocument(formData: FormData) {\r\n    const response = await this.api.post('/documents/upload', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async downloadDocument(id: string) {\r\n    const response = await this.api.get(`/documents/${id}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Dashboard statistics\r\n  async getDashboardStats() {\r\n    const response = await this.api.get('/dashboard/stats');\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Notifications\r\n  async getNotifications(params?: { read?: boolean; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/notifications', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async markNotificationAsRead(id: string) {\r\n    const response = await this.api.patch(`/notifications/${id}/read`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Procurement endpoints\r\n  async getTenders(params?: { status?: string; category?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/tenders', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getTender(id: string) {\r\n    const response = await this.api.get(`/procurement/tenders/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async payForTenderAccess(tenderId: string, paymentData: TenderPaymentData) {\r\n    const response = await this.api.post(`/procurement/tenders/${tenderId}/pay-access`, paymentData);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async downloadTenderDocument(documentId: string) {\r\n    const response = await this.api.get(`/procurement/documents/${documentId}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getMyBids(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/my-bids', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getBid(id: string) {\r\n    const response = await this.api.get(`/procurement/bids/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async submitBid(formData: FormData) {\r\n    const response = await this.api.post('/procurement/bids', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async updateBid(id: string, formData: FormData) {\r\n    const response = await this.api.put(`/procurement/bids/${id}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getProcurementPayments(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/payments', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getProcurementPayment(id: string) {\r\n    const response = await this.api.get(`/procurement/payments/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Consumer Affairs endpoints\r\n  async getComplaints(params?: { status?: string; category?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/consumer-affairs/complaints', { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async getComplaint(id: string) {\r\n    const response = await this.api.get(`/consumer-affairs/complaints/${id}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async submitComplaint(complaintData: ComplaintData) {\r\n    const formData = new FormData();\r\n    formData.append('title', complaintData.title);\r\n    formData.append('description', complaintData.description);\r\n    formData.append('category', complaintData.category);\r\n\r\n    if (complaintData.attachments) {\r\n      complaintData.attachments.forEach((file, index) => {\r\n        formData.append(`attachments[${index}]`, file);\r\n      });\r\n    }\r\n\r\n    const response = await this.api.post('/consumer-affairs/complaints', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async updateComplaint(id: string, updates: Partial<ComplaintData>) {\r\n    const response = await this.api.put(`/consumer-affairs/complaints/${id}`, updates);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  async downloadComplaintAttachment(complaintId: string, attachmentId: string) {\r\n    const response = await this.api.get(`/consumer-affairs/complaints/${complaintId}/attachments/${attachmentId}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const customerApi = new CustomerApiService();\r\n\r\n// Export axios instance for direct use if needed\r\nexport { customerApiClient };\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAOA,oBAAoB;AACpB,MAAM,eAAe,6DAAmC;AAExD,mEAAmE;AACnE,MAAM,oBAAmC,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACpD,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,qDAAqD;AACrD,MAAM,wBAAuC,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACxD,SAAS,GAAG,aAAa,KAAK,CAAC;IAC/B,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,sBAAsB,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC7C,CAAC;IACC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,uCAAuC;YACjD,QAAQ,SAAS,MAAM;YACvB,YAAY,SAAS,UAAU;YAC/B,KAAK,SAAS,MAAM,CAAC,GAAG;QAC1B;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,wCAA4C;QAC1C,QAAQ,KAAK,CAAC,wCAAwC;YACpD,SAAS,OAAO,WAAW;YAC3B,MAAM,OAAO,QAAQ;YACrB,QAAQ,OAAO,UAAU,UAAU;YACnC,YAAY,OAAO,UAAU,cAAc;YAC3C,KAAK,OAAO,QAAQ,OAAO;YAC3B,QAAQ,OAAO,QAAQ,UAAU;YACjC,SAAS,OAAO,QAAQ,WAAW;YACnC,cAAc,OAAO,gBAAgB;YACrC,cAAc,OAAO,UAAU,QAAQ;YACvC,aAAa,OAAO,QAAQ,QAAQ;YACpC,SAAS,OAAO,QAAQ,WAAW;QACrC;IACF;IAEA,wDAAwD;IACxD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,wCAAwC;AACxC,kBAAkB,YAAY,CAAC,OAAO,CAAC,GAAG,CACxC,CAAC;IACC,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;IAC1B,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,2DAA2D;AAC3D,kBAAkB,YAAY,CAAC,QAAQ,CAAC,GAAG,CACzC,CAAC;IACC,OAAO;AACT,GACA,OAAO;IACL,MAAM,kBAAkB,MAAM,MAAM;IAKpC,2BAA2B;IAC3B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,IAAI,CAAC,gBAAgB,MAAM,EAAE;YAC3B,gBAAgB,MAAM,GAAG;YAEzB,0DAA0D;YAC1D,MAAM,aAAa,MAAM,QAAQ,CAAC,OAAO,CAAC,cAAc;YACxD,MAAM,QAAQ,aAAa,SAAS,cAAc,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,GAAG,gBAAgB,WAAW,IAAI,IAAI;YAExH,gBAAgB,WAAW,GAAG,CAAC,gBAAgB,WAAW,IAAI,CAAC,IAAI;YAEnE,gCAAgC;YAChC,IAAI,gBAAgB,WAAW,IAAI,GAAG;gBACpC,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,MAAM,YAAY,EAAE,gBAAgB,WAAW,CAAC,CAAC,CAAC;gBAE/F,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,OAAO,kBAAkB;YAC3B;QACF;IACF;IAEA,0BAA0B;IAC1B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,kDAAkD;QAClD,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM;IACJ,IAAmB;IAClB,kBAAiD,IAAI,MAAM;IAEnE,aAAc;QACZ,IAAI,CAAC,GAAG,GAAG;IACb;IAEA,+BAA+B;IAC/B,MAAc,mBAAsB,GAAW,EAAE,SAA2B,EAAc;QACxF,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM;YACjC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;QAClC;QAEA,MAAM,UAAU,YAAY,OAAO,CAAC;YAClC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QAC9B;QAEA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK;QAC9B,OAAO;IACT;IAEA,iBAAiB;IACjB,aAAa,KAAa,EAAE;QAC1B,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACvE;IAEA,oBAAoB;IACpB,kBAAkB;QAChB,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB;IAC1D;IAEA,MAAM,SAAS;QACb,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe;QACnB,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,sBAAsB,MAAc,EAAE,MAAc,EAAE;QAC1D,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,iBAAiB;YAAE,SAAS;YAAQ;QAAO;QAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,UAAU,IAAuD,EAAE;QACvE,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,eAAe;QAEjE,oDAAoD;QACpD,IAAI,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,MAAM;YACtC,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;YAElD,sDAAsD;YACtD,MAAM,iBAAiB;gBACrB,cAAc,SAAS,YAAY;gBACnC,MAAM;oBACJ,IAAI,SAAS,IAAI,CAAC,OAAO;oBACzB,WAAW,SAAS,IAAI,CAAC,UAAU;oBACnC,UAAU,SAAS,IAAI,CAAC,SAAS;oBACjC,OAAO,SAAS,IAAI,CAAC,KAAK;oBAC1B,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;oBAChC,SAAS,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC;oBAC9C,cAAc,SAAS,IAAI,CAAC,aAAa;oBACzC,WAAW,SAAS,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;oBAC7D,WAAW,SAAS,IAAI,CAAC,UAAU;oBACnC,kBAAkB,SAAS,IAAI,CAAC,iBAAiB;oBACjD,oBAAoB,SAAS,IAAI,CAAC,kBAAkB;gBACtD;YACF;YAEA,OAAO;QACT;QAEA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc;YAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;YACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B;IACF;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;QACtD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,kBAAkB,gBAAuC,EAAE;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,eAAe;QACnB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB;QACxD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;QACpD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,YAAY,WAA4B,EAAE;QAC9C,MAAM,EAAE,UAAU,EAAE,GAAG,YAAY,GAAG;QACtC,IAAI,CAAC,YAAY;YACf,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,qBAAqB,UAAkB,EAAE,QAAgB,EAAE;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE,mBAAmB,YAAY,WAAW,EAAE,mBAAmB,WAAW;QAC1I,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,EAAU,EAAE;QAC9B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC5D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,gBAAgB,YAA6B,EAAE;QACnD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,gBAAgB,MAA0C,EAAE;QAChE,sDAAsD;QACtD,MAAM,cAAc,SAAS;YAC3B,MAAM,OAAO,OAAO,IAAI,KAAK;YAC7B,OAAO,OAAO,OAAO,KAAK,KAAK;QACjC,IAAI;YAAE,MAAM;YAAG,OAAO;QAAK;QAE3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,qBAAqB;YACvD,QAAQ;QACV;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,wBAAwB,UAAkB,EAAoC;QAClF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,YAAY;QACxE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,IAAI;IACzC;IAEA,oBAAoB;IACpB,MAAM,YAAY,MAA2D,EAAE;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa;YAAE;QAAO;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACrD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,yBAAyB,eAAuC,EAAE;QACtE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,yBAAyB;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,MAA0C,EAAE;QAChE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;YAAE;QAAO;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QAC1D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,qBAAqB,MAA0C,EAAE;QACrE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAuB;YAAE;QAAO;QACpE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,2BAA2B,aAAqB,EAAE;QACtD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;QAC1F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB,aAAqB,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;QAC5F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,mBAAmB,EAAU,EAAE;QACnC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wBAAwB;IACxB,MAAM,gBAAgB,MAA2D,EAAE;QACjF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB;YAAE;QAAO;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QACzD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,kBAAkB,eAAoB,EAAE;QAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB;QACtD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,kBAAkB,EAAU,EAAE,eAAgD,EAAE;QACpF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC3D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wCAAwC;IACxC,MAAM,YAAY,MAAsH,EAAE;QACxI,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kCAAkC;YAAE;QAAO;QAC/E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,IAAI;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,QAAkB,EAAE;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,UAAU;YAClE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,mBAAmB,SAAiB,EAAE,WAAgB,EAAE;QAC5D,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2BAA2B,EAAE,UAAU,iBAAiB,CAAC,EAAE,aAAa;YAC5G,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,qBAAqB,SAAiB,EAAE,QAAkB,EAAE;QAChE,IAAI;YACF,gDAAgD;YAChD,QAAQ,GAAG,CAAC;YAEZ,qEAAqE;YACrE,MAAM,mBAAmB,IAAI;YAE7B,2CAA2C;YAC3C,MAAM,OAAO,SAAS,GAAG,CAAC;YAC1B,IAAI,CAAC,MAAM;gBACT,MAAM,IAAI,MAAM;YAClB;YAEA,iBAAiB,MAAM,CAAC,QAAQ;YAChC,iBAAiB,MAAM,CAAC,iBAAiB;YACzC,iBAAiB,MAAM,CAAC,eAAe;YACvC,iBAAiB,MAAM,CAAC,aAAa;YACrC,iBAAiB,MAAM,CAAC,aAAa,KAAK,IAAI;YAE9C,MAAM,iBAAiB,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,kBAAkB;gBAChF,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,MAAM,eAAe,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAExC,IAAI,CAAC,aAAa,OAAO,IAAI,CAAC,aAAa,IAAI,EAAE,aAAa;gBAC5D,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,aAAa,aAAa,IAAI,CAAC,WAAW;YAChD,QAAQ,GAAG,CAAC,iDAAiD;YAE7D,oDAAoD;YACpD,QAAQ,GAAG,CAAC;YAEZ,wCAAwC;YACxC,MAAM,cAAmB;gBAAE;YAAW;YAEtC,2CAA2C;YAC3C,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,SAAS,OAAO,GAAI;gBAC7C,IAAI,QAAQ,UAAU,QAAQ,mBAAmB,QAAQ,iBAAiB,QAAQ,aAAa;oBAC7F,WAAW,CAAC,IAAI,GAAG;gBACrB;YACF;YAEA,MAAM,eAAe,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,2BAA2B,EAAE,UAAU,iBAAiB,CAAC,EAAE,aAAa;gBAChH,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAE5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM;QACR;IACF;IAEA,MAAM,uBAAuB;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB,aAAqB,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,+BAA+B,EAAE,eAAe;QACrF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,mBAAmB,SAAiB,EAAE;QAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,WAAW;QAC7E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wCAAwC;IACxC,MAAM,YAAY,MAA4E,EAAE;QAC9F,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,sBAAsB;YAAE;QAAO;QACnE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,IAAI;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,oBAAoB,UAAkB,EAAE,QAAgB,EAAE;QAC9D,yDAAyD;QACzD,IAAI,eAAe,eAAe;YAChC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,+BAA+B,EAAE,UAAU;YAChF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B;QACA,sDAAsD;QACtD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,WAAW,CAAC,EAAE,UAAU;QAChF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,4BAA4B,aAAqB,EAAE;QACvD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,+BAA+B,EAAE,cAAc,OAAO,CAAC;QAC5F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,gBAAgB,SAAiB,EAAiB;QACtD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU,SAAS,CAAC,EAAE;YAC9E,cAAc;QAChB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,qBAAqB;IACrB,MAAM,aAAa,MAAY,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc;YAAE;QAAO;QAC3D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,eAAe,QAAkB,EAAE;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,UAAU;YAClE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,iBAAiB,EAAU,EAAE;QACjC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS,CAAC,EAAE;YAC/D,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,oBAAoB;QACxB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,iBAAiB,MAA0D,EAAE;QACjF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;YAAE;QAAO;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB,EAAU,EAAE;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,GAAG,KAAK,CAAC;QACjE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,wBAAwB;IACxB,MAAM,WAAW,MAA8E,EAAE;QAC/F,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,wBAAwB;YAAE;QAAO;QACrE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,UAAU,EAAU,EAAE;QAC1B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,IAAI;QAChE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,mBAAmB,QAAgB,EAAE,WAA8B,EAAE;QACzE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qBAAqB,EAAE,SAAS,WAAW,CAAC,EAAE;QACpF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB,UAAkB,EAAE;QAC/C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,WAAW,SAAS,CAAC,EAAE;YACnF,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,UAAU,MAA2D,EAAE;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,wBAAwB;YAAE;QAAO;QACrE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,OAAO,EAAU,EAAE;QACvB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,UAAU,QAAkB,EAAE;QAClC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,UAAU;YAClE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,UAAU,EAAU,EAAE,QAAkB,EAAE;QAC9C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,EAAE,UAAU;YACvE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,uBAAuB,MAA2D,EAAE;QACxF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,yBAAyB;YAAE;QAAO;QACtE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,sBAAsB,EAAU,EAAE;QACtC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,sBAAsB,EAAE,IAAI;QACjE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM,cAAc,MAA8E,EAAE;QAClG,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,gCAAgC;YAAE;QAAO;QAC7E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,aAAa,EAAU,EAAE;QAC7B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI;QACxE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,gBAAgB,aAA4B,EAAE;QAClD,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS,cAAc,KAAK;QAC5C,SAAS,MAAM,CAAC,eAAe,cAAc,WAAW;QACxD,SAAS,MAAM,CAAC,YAAY,cAAc,QAAQ;QAElD,IAAI,cAAc,WAAW,EAAE;YAC7B,cAAc,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM;gBACvC,SAAS,MAAM,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,EAAE;YAC3C;QACF;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gCAAgC,UAAU;YAC7E,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,gBAAgB,EAAU,EAAE,OAA+B,EAAE;QACjE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI,EAAE;QAC1E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,MAAM,4BAA4B,WAAmB,EAAE,YAAoB,EAAE;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,YAAY,aAAa,EAAE,aAAa,SAAS,CAAC,EAAE;YACtH,cAAc;QAChB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 1225, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/lib/public-api.ts"], "sourcesContent": ["import axios, { AxiosError, AxiosInstance, AxiosResponse } from \"axios\";\r\nimport { processApiResponse } from \"./authUtils\";\r\n\r\n\r\n// API Configuration\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n\r\n// Public API Client (no authentication required)\r\nconst publicApiClient: AxiosInstance = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  timeout: 120000,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json',\r\n  },\r\n});\r\n\r\n// Add debug logging to public client (only in development)\r\npublicApiClient.interceptors.request.use(\r\n  (config) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Public API Request:', {\r\n        url: `${config.baseURL}${config.url}`,\r\n        method: config.method,\r\n        headers: config.headers,\r\n        data: config.data\r\n      });\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.error('Public API Request Error:', error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Public API response interceptor\r\npublicApiClient.interceptors.response.use(\r\n  (response: AxiosResponse) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Public API Response:', {\r\n        url: response.config.url,\r\n        status: response.status,\r\n        data: response.data\r\n      });\r\n    }\r\n    return response;\r\n  },\r\n  (error: AxiosError) => {\r\n    console.error('Public API Response Error:', {\r\n      url: error.config?.url,\r\n      status: error.response?.status,\r\n      message: error.message,\r\n      data: error.response?.data\r\n    });\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Public API Service Class\r\nexport class PublicApiService {\r\n  public api: AxiosInstance;\r\n\r\n  constructor() {\r\n    this.api = publicApiClient;\r\n  }\r\n\r\n  // Verify license authenticity\r\n  async verifyLicense(licenseNumber: string, verificationCode?: string) {\r\n    const params = verificationCode ? { code: verificationCode } : {};\r\n    const response = await this.api.get(`/public/verify/${licenseNumber}`, { params });\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Check license status (lightweight check)\r\n  async checkLicenseStatus(licenseNumber: string) {\r\n    const response = await this.api.get(`/public/verify-status/${licenseNumber}`);\r\n    return processApiResponse(response);\r\n  }\r\n\r\n  // Get verification statistics\r\n  async getVerificationStats() {\r\n    const response = await this.api.get('/public/verification-stats');\r\n    return processApiResponse(response);\r\n  }\r\n}\r\n\r\nexport const publicApi = new PublicApiService();\r\n\r\n// Export axios instance for direct use if needed\r\nexport { publicApiClient };\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGA,oBAAoB;AACpB,MAAM,eAAe,6DAAmC;AAExD,iDAAiD;AACjD,MAAM,kBAAiC,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAClD,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,2DAA2D;AAC3D,gBAAgB,YAAY,CAAC,OAAO,CAAC,GAAG,CACtC,CAAC;IACC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,uBAAuB;YACjC,KAAK,GAAG,OAAO,OAAO,GAAG,OAAO,GAAG,EAAE;YACrC,QAAQ,OAAO,MAAM;YACrB,SAAS,OAAO,OAAO;YACvB,MAAM,OAAO,IAAI;QACnB;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,6BAA6B;IAC3C,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,kCAAkC;AAClC,gBAAgB,YAAY,CAAC,QAAQ,CAAC,GAAG,CACvC,CAAC;IACC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,wBAAwB;YAClC,KAAK,SAAS,MAAM,CAAC,GAAG;YACxB,QAAQ,SAAS,MAAM;YACvB,MAAM,SAAS,IAAI;QACrB;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,8BAA8B;QAC1C,KAAK,MAAM,MAAM,EAAE;QACnB,QAAQ,MAAM,QAAQ,EAAE;QACxB,SAAS,MAAM,OAAO;QACtB,MAAM,MAAM,QAAQ,EAAE;IACxB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM;IACJ,IAAmB;IAE1B,aAAc;QACZ,IAAI,CAAC,GAAG,GAAG;IACb;IAEA,8BAA8B;IAC9B,MAAM,cAAc,aAAqB,EAAE,gBAAyB,EAAE;QACpE,MAAM,SAAS,mBAAmB;YAAE,MAAM;QAAiB,IAAI,CAAC;QAChE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,eAAe,EAAE;YAAE;QAAO;QAChF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,2CAA2C;IAC3C,MAAM,mBAAmB,aAAqB,EAAE;QAC9C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,sBAAsB,EAAE,eAAe;QAC5E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,uBAAuB;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF;AAEO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/lib/index.ts"], "sourcesContent": ["export * from './customer-api';\r\nexport * from './public-api';\r\nexport * from './authUtils';\r\nexport * from './apiClient';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1334, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/userService.ts"], "sourcesContent": ["import { AxiosError } from 'axios';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { ChangePasswordDto, CreateUserDto, UpdateProfileDto, UpdateUserDto, User } from '@/types/user';\r\nimport { PaginatedResponse, PaginateQuery } from '@/types';\r\nimport { apiClient, usersApiClient } from '@/lib';\r\n\r\n// Re-export for backward compatibility\r\n\r\nexport const userService = {\r\n  // Get all users with pagination\r\n  async getUsers(query: PaginateQuery = {}): Promise<PaginatedResponse<User>> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await usersApiClient.get(`?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get user by ID\r\n  async getUser(id: string): Promise<User> {\r\n    const response = await usersApiClient.get(`/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get user emails for predictive input\r\n  async getUserEmails(searchTerm?: string): Promise<string[]> {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      if (searchTerm) params.set('search', searchTerm);\r\n      params.set('limit', '20'); // Limit to 20 suggestions\r\n\r\n      const response = await apiClient.get(`/users?${params.toString()}`);\r\n      const usersData = processApiResponse(response);\r\n\r\n      // Extract emails from users\r\n      const emails = usersData.data?.map((user: User) => user.email).filter(Boolean) || [];\r\n      return emails;\r\n    } catch (error) {\r\n      console.warn('Failed to fetch user emails:', error);\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // Get user by ID (alias for consistency)\r\n  async getUserById(id: string): Promise<User> {\r\n    return this.getUser(id);\r\n  },\r\n\r\n  // Get current user profile\r\n  async getProfile(): Promise<User> {\r\n    const response = await usersApiClient.get('/profile');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new user\r\n  async createUser(userData: CreateUserDto): Promise<User> {\r\n    const response = await usersApiClient.post('', userData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update user\r\n  async updateUser(id: string, userData: UpdateUserDto): Promise<User> {\r\n    const response = await usersApiClient.put(`/${id}`, userData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update current user profile\r\n  async updateProfile(userData: UpdateProfileDto): Promise<User> {\r\n    const response = await usersApiClient.put('/profile', userData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Change password\r\n  async changePassword(passwordData: ChangePasswordDto): Promise<{ message: string }> {\r\n    const response = await usersApiClient.put('/profile/password', passwordData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Upload avatar\r\n  async uploadAvatar(file: File): Promise<User> {\r\n    console.log('userService: uploadAvatar called', {\r\n      fileName: file.name,\r\n      fileSize: file.size,\r\n      fileType: file.type\r\n    });\r\n\r\n    const formData = new FormData();\r\n    formData.append('avatar', file);\r\n\r\n\r\n    try {\r\n      const response = await usersApiClient.post('/profile/avatar', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n\r\n      return processApiResponse(response);\r\n    } catch (error: unknown) {\r\n      const axiosError = error as AxiosError;\r\n      console.error('userService: Upload failed', {\r\n        status: axiosError.response?.status,\r\n        statusText: axiosError.response?.statusText,\r\n        data: axiosError.response?.data,\r\n        message: axiosError.message\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Remove avatar\r\n  async removeAvatar(): Promise<User> {\r\n    const response = await usersApiClient.delete('/profile/avatar');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete user\r\n  async deleteUser(id: string): Promise<void> {\r\n    await usersApiClient.delete(`/${id}`);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AACA;AAGA;AAAA;;;AAIO,MAAM,cAAc;IACzB,gCAAgC;IAChC,MAAM,UAAS,QAAuB,CAAC,CAAC;QACtC,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;QACjE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,iBAAiB;IACjB,MAAM,SAAQ,EAAU;QACtB,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uCAAuC;IACvC,MAAM,eAAc,UAAmB;QACrC,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,YAAY,OAAO,GAAG,CAAC,UAAU;YACrC,OAAO,GAAG,CAAC,SAAS,OAAO,0BAA0B;YAErD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,QAAQ,IAAI;YAClE,MAAM,YAAY,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAErC,4BAA4B;YAC5B,MAAM,SAAS,UAAU,IAAI,EAAE,IAAI,CAAC,OAAe,KAAK,KAAK,EAAE,OAAO,YAAY,EAAE;YACpF,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,gCAAgC;YAC7C,OAAO,EAAE;QACX;IACF;IAEA,yCAAyC;IACzC,MAAM,aAAY,EAAU;QAC1B,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,2BAA2B;IAC3B,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC;QAC1C,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,kBAAkB;IAClB,MAAM,YAAW,QAAuB;QACtC,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,IAAI;QAC/C,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,cAAc;IACd,MAAM,YAAW,EAAU,EAAE,QAAuB;QAClD,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE;QACpD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,eAAc,QAA0B;QAC5C,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,YAAY;QACtD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,kBAAkB;IAClB,MAAM,gBAAe,YAA+B;QAClD,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,qBAAqB;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,cAAa,IAAU;QAC3B,QAAQ,GAAG,CAAC,oCAAoC;YAC9C,UAAU,KAAK,IAAI;YACnB,UAAU,KAAK,IAAI;YACnB,UAAU,KAAK,IAAI;QACrB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,UAAU;QAG1B,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,mBAAmB,UAAU;gBACtE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAgB;YACvB,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,8BAA8B;gBAC1C,QAAQ,WAAW,QAAQ,EAAE;gBAC7B,YAAY,WAAW,QAAQ,EAAE;gBACjC,MAAM,WAAW,QAAQ,EAAE;gBAC3B,SAAS,WAAW,OAAO;YAC7B;YACA,MAAM;QACR;IACF;IAEA,gBAAgB;IAChB,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;QAC7C,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,cAAc;IACd,MAAM,YAAW,EAAU;QACzB,MAAM,uHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI;IACtC;AACF", "debugId": null}}, {"offset": {"line": 1458, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\r\nimport { authService } from '../services/auth.service';\r\nimport Cookies from 'js-cookie';\r\nimport { startTokenValidationTimer, isTokenExpired } from '../lib/authUtils';\r\nimport { AuthResponse, User, RegisterData, UpdateUserData } from '@/types';\r\nimport { userService } from '@/services/userService';\r\n\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  token: string | null;\r\n  login: (email: string, password: string, rememberMe?: boolean) => Promise<AuthResponse>;\r\n  completeTwoFactorLogin: (token: string, userData: User, rememberMe?: boolean) => Promise<void>;\r\n  register: (userData: RegisterData) => Promise<AuthResponse>;\r\n  logout: () => void;\r\n  updateUser: (user: UpdateUserData) => void;\r\n  loading: boolean;\r\n  isAuthenticated: boolean;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface AuthProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [token, setToken] = useState<string | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [mounted, setMounted] = useState(false);\r\n  // Set mounted to true after hydration\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Start token validation timer when mounted and authenticated\r\n  useEffect(() => {\r\n    if (!mounted || !user || !token) return;\r\n    // Start periodic token validation (check every 5 minutes instead of 1 minute)\r\n    const validationTimer = startTokenValidationTimer(300000);\r\n    return () => {\r\n      clearInterval(validationTimer);\r\n    };\r\n  }, [mounted, user, token]);\r\n\r\n  useEffect(() => {\r\n    if (!mounted) return;\r\n    // Execute immediately instead of waiting for next render cycle\r\n    initAuth();\r\n  }, [mounted]);\r\n\r\n  // Check for existing token on mount with validation\r\n  const initAuth = () => {\r\n    // Get saved data from cookies\r\n    const savedToken = Cookies.get('auth_token');\r\n    const savedUser = Cookies.get('auth_user');\r\n    if (savedToken && savedUser) {\r\n      try {\r\n        const user = JSON.parse(savedUser);\r\n        // Validate token is not expired\r\n        if (!isTokenExpired(savedToken)) {\r\n          setToken(savedToken);\r\n          setUser(user);\r\n          authService.setAuthToken(savedToken);\r\n        } else {\r\n          Cookies.remove('auth_token');\r\n          Cookies.remove('auth_user');\r\n          authService.clearAuthToken();\r\n        }\r\n      } catch (error) {\r\n        console.error('AuthContext: Failed to parse saved user data:', error);\r\n        Cookies.remove('auth_token');\r\n        Cookies.remove('auth_user');\r\n        authService.clearAuthToken();\r\n      }\r\n    } else {\r\n      console.log('AuthContext: No saved authentication data found');\r\n    }\r\n    setLoading(false);\r\n  }\r\n\r\n  const login = async (email: string, password: string, rememberMe: boolean = false): Promise<AuthResponse> => {\r\n    try {\r\n      const response: AuthResponse = await authService.login({ email, password });\r\n      // Validate response structure for normal login\r\n      if (!response || !response.user) {\r\n        throw new Error('Invalid response from authentication service');\r\n      }\r\n\r\n      // Store remember me preference for 2FA completion\r\n      if (rememberMe) {\r\n        sessionStorage.setItem('remember_me', 'true');\r\n      } else {\r\n        sessionStorage.removeItem('remember_me');\r\n      }\r\n\r\n      // Check if 2FA is required (backend returns empty access_token when 2FA is needed)\r\n      const requires2FA = response.user.two_factor_enabled && (!response.access_token || response.access_token === '');\r\n\r\n      if (!requires2FA) {\r\n        // No 2FA required, set auth context immediately\r\n        const roles = response.user.roles ?? [];\r\n        const user = {\r\n          ...response.user,\r\n          roles,\r\n          isAdmin: roles.includes('administrator'),\r\n          isCustomer: roles.includes('customer')\r\n        };\r\n        Cookies.set('auth_user', JSON.stringify(user));\r\n        Cookies.set('auth_token', response.access_token || '');\r\n        initAuth();\r\n      }\r\n\r\n      return response;\r\n    } catch (error) {\r\n      console.error('AuthContext: Login failed', error);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const completeTwoFactorLogin = async (token: string, userData: User, rememberMe: boolean = false): Promise<void> => {\r\n    try {\r\n      // Ensure roles is an array\r\n      const roles: string[] = Array.isArray(userData.roles) ? userData.roles : [];\r\n\r\n      // Add computed isAdmin property for backward compatibility\r\n      const user = {\r\n        ...userData,\r\n        roles,\r\n        isAdmin: roles.includes('administrator'),\r\n        isCustomer: roles.includes('customer'),\r\n      };\r\n\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.log('AuthContext: 2FA login successful, setting user', user);\r\n      }\r\n      \r\n      setToken(token);\r\n      setUser(user);\r\n\r\n      // Set cookies with appropriate expiration based on rememberMe\r\n      const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\r\n      Cookies.set('auth_token', token, { expires: cookieExpiration });\r\n      Cookies.set('auth_user', JSON.stringify(user), { expires: cookieExpiration });\r\n      authService.setAuthToken(token);\r\n    } catch (error) {\r\n      console.error('AuthContext: 2FA login completion failed', error);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const register = async (userData: RegisterData): Promise<AuthResponse> => {\r\n    const result = await authService.register(userData);\r\n\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('AuthContext: Registration successful - user should login manually');\r\n    }\r\n\r\n    // Don't automatically log in the user after registration\r\n    // User should be redirected to login page to manually log in\r\n    // This follows the requirement that after account creation,\r\n    // users should be redirected to login page, not dashboard\r\n\r\n    return result;\r\n  };\r\n\r\n  const logout = (): void => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('AuthContext: Logging out user');\r\n    }\r\n\r\n    // Clear state\r\n    setUser(null);\r\n    setToken(null);\r\n\r\n    // Remove from cookies\r\n    Cookies.remove('auth_token');\r\n    Cookies.remove('auth_user');\r\n\r\n    // Clear auth service token\r\n    authService.clearAuthToken();\r\n\r\n    // Clear any other localStorage items related to auth\r\n    if (mounted && typeof window !== 'undefined') {\r\n      localStorage.removeItem('auth_token');\r\n      localStorage.removeItem('user_preferences');\r\n      // Clear any cached data\r\n      sessionStorage.clear();\r\n    }\r\n\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('AuthContext: Logout complete');\r\n    }\r\n  };\r\n\r\n  const updateUser = (updatedUser: UpdateUserData): void => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('AuthContext: Updating user', updatedUser);\r\n    }\r\n\r\n    // Convert roles to string array if they're objects\r\n    let roles: string[] = [];\r\n    if (updatedUser.roles) {\r\n      roles = updatedUser.roles.map((role: string | { name?: string; role_name?: string }) =>\r\n        typeof role === 'string' ? role : role.name || role.role_name || 'unknown'\r\n      );\r\n    }\r\n\r\n    userService.getUserById(updatedUser.user_id).then(user => {\r\n      console.log(\"===================user\")\r\n      console.log(user)\r\n      user.isCustomer =  roles.includes('customer')\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.log('AuthContext: Setting updated user', updatedUser);\r\n      }\r\n      setUser(user);\r\n      // Update cookies with new user data - use existing token expiration\r\n      const existingToken = Cookies.get('auth_token');\r\n      if (existingToken) {\r\n        // Try to determine original expiration from token or use default\r\n        const cookieExpiration = 1; // Default to 1 day for user updates\r\n        Cookies.set('auth_user', JSON.stringify(updatedUser), { expires: cookieExpiration });\r\n      }\r\n    })\r\n    // Ensure isAdmin property is set for backward compatibility\r\n\r\n  };\r\n\r\n  const value: AuthContextType = {\r\n    user,\r\n    token,\r\n    login,\r\n    completeTwoFactorLogin,\r\n    register,\r\n    logout,\r\n    updateUser,\r\n    loading: loading || !mounted,\r\n    isAuthenticated: mounted && !!user && !!token,\r\n    \r\n  };\r\n\r\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AAqBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO;QACjC,8EAA8E;QAC9E,MAAM,kBAAkB,CAAA,GAAA,uHAAA,CAAA,4BAAyB,AAAD,EAAE;QAClD,OAAO;YACL,cAAc;QAChB;IACF,GAAG;QAAC;QAAS;QAAM;KAAM;IAEzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QACd,+DAA+D;QAC/D;IACF,GAAG;QAAC;KAAQ;IAEZ,oDAAoD;IACpD,MAAM,WAAW;QACf,8BAA8B;QAC9B,MAAM,aAAa,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAC/B,MAAM,YAAY,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAC9B,IAAI,cAAc,WAAW;YAC3B,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC;gBACxB,gCAAgC;gBAChC,IAAI,CAAC,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;oBAC/B,SAAS;oBACT,QAAQ;oBACR,kIAAA,CAAA,cAAW,CAAC,YAAY,CAAC;gBAC3B,OAAO;oBACL,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;oBACf,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;oBACf,kIAAA,CAAA,cAAW,CAAC,cAAc;gBAC5B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iDAAiD;gBAC/D,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;gBACf,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;gBACf,kIAAA,CAAA,cAAW,CAAC,cAAc;YAC5B;QACF,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QACA,WAAW;IACb;IAEA,MAAM,QAAQ,OAAO,OAAe,UAAkB,aAAsB,KAAK;QAC/E,IAAI;YACF,MAAM,WAAyB,MAAM,kIAAA,CAAA,cAAW,CAAC,KAAK,CAAC;gBAAE;gBAAO;YAAS;YACzE,+CAA+C;YAC/C,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,EAAE;gBAC/B,MAAM,IAAI,MAAM;YAClB;YAEA,kDAAkD;YAClD,IAAI,YAAY;gBACd,eAAe,OAAO,CAAC,eAAe;YACxC,OAAO;gBACL,eAAe,UAAU,CAAC;YAC5B;YAEA,mFAAmF;YACnF,MAAM,cAAc,SAAS,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC,SAAS,YAAY,IAAI,SAAS,YAAY,KAAK,EAAE;YAE/G,IAAI,CAAC,aAAa;gBAChB,gDAAgD;gBAChD,MAAM,QAAQ,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;gBACvC,MAAM,OAAO;oBACX,GAAG,SAAS,IAAI;oBAChB;oBACA,SAAS,MAAM,QAAQ,CAAC;oBACxB,YAAY,MAAM,QAAQ,CAAC;gBAC7B;gBACA,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,aAAa,KAAK,SAAS,CAAC;gBACxC,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc,SAAS,YAAY,IAAI;gBACnD;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,MAAM,yBAAyB,OAAO,OAAe,UAAgB,aAAsB,KAAK;QAC9F,IAAI;YACF,2BAA2B;YAC3B,MAAM,QAAkB,MAAM,OAAO,CAAC,SAAS,KAAK,IAAI,SAAS,KAAK,GAAG,EAAE;YAE3E,2DAA2D;YAC3D,MAAM,OAAO;gBACX,GAAG,QAAQ;gBACX;gBACA,SAAS,MAAM,QAAQ,CAAC;gBACxB,YAAY,MAAM,QAAQ,CAAC;YAC7B;YAEA,wCAA4C;gBAC1C,QAAQ,GAAG,CAAC,mDAAmD;YACjE;YAEA,SAAS;YACT,QAAQ;YAER,8DAA8D;YAC9D,MAAM,mBAAmB,aAAa,KAAK,GAAG,0CAA0C;YACxF,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc,OAAO;gBAAE,SAAS;YAAiB;YAC7D,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,aAAa,KAAK,SAAS,CAAC,OAAO;gBAAE,SAAS;YAAiB;YAC3E,kIAAA,CAAA,cAAW,CAAC,YAAY,CAAC;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,MAAM;QACR;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,MAAM,SAAS,MAAM,kIAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;QAE1C,wCAA4C;YAC1C,QAAQ,GAAG,CAAC;QACd;QAEA,yDAAyD;QACzD,6DAA6D;QAC7D,4DAA4D;QAC5D,0DAA0D;QAE1D,OAAO;IACT;IAEA,MAAM,SAAS;QACb,wCAA4C;YAC1C,QAAQ,GAAG,CAAC;QACd;QAEA,cAAc;QACd,QAAQ;QACR,SAAS;QAET,sBAAsB;QACtB,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QAEf,2BAA2B;QAC3B,kIAAA,CAAA,cAAW,CAAC,cAAc;QAE1B,qDAAqD;QACrD;;QAOA,wCAA4C;YAC1C,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,8BAA8B;QAC5C;QAEA,mDAAmD;QACnD,IAAI,QAAkB,EAAE;QACxB,IAAI,YAAY,KAAK,EAAE;YACrB,QAAQ,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,OAC7B,OAAO,SAAS,WAAW,OAAO,KAAK,IAAI,IAAI,KAAK,SAAS,IAAI;QAErE;QAEA,8HAAA,CAAA,cAAW,CAAC,WAAW,CAAC,YAAY,OAAO,EAAE,IAAI,CAAC,CAAA;YAChD,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;YACZ,KAAK,UAAU,GAAI,MAAM,QAAQ,CAAC;YAClC,wCAA4C;gBAC1C,QAAQ,GAAG,CAAC,qCAAqC;YACnD;YACA,QAAQ;YACR,oEAAoE;YACpE,MAAM,gBAAgB,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAClC,IAAI,eAAe;gBACjB,iEAAiE;gBACjE,MAAM,mBAAmB,GAAG,oCAAoC;gBAChE,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,aAAa,KAAK,SAAS,CAAC,cAAc;oBAAE,SAAS;gBAAiB;YACpF;QACF;IACA,4DAA4D;IAE9D;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS,WAAW,CAAC;QACrB,iBAAiB,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC;IAE1C;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C", "debugId": null}}, {"offset": {"line": 1715, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/Loader.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport Image from 'next/image'\r\n\r\nconst Loader = ({ message = 'Loading...' }) => {\r\n  return (\r\n    <div className=\"text-center\">\r\n    <div className=\"relative w-20 h-20 mx-auto\">\r\n        {/* SVG Spinner with tapered stroke ends */}\r\n        <svg\r\n        className=\"absolute inset-0 animate-spin\"\r\n        viewBox=\"0 0 50 50\"\r\n        fill=\"none\"\r\n        >\r\n        <defs>\r\n            <linearGradient id=\"fadeGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n            <stop offset=\"0%\" stopColor=\"#dc2626\" stopOpacity=\"0\" />\r\n            <stop offset=\"20%\" stopColor=\"#dc2626\" stopOpacity=\"1\" />\r\n            <stop offset=\"80%\" stopColor=\"#dc2626\" stopOpacity=\"1\" />\r\n            <stop offset=\"100%\" stopColor=\"#dc2626\" stopOpacity=\"0\" />\r\n            </linearGradient>\r\n        </defs>\r\n\r\n        <circle\r\n            cx=\"25\"\r\n            cy=\"25\"\r\n            r=\"20\"\r\n            stroke=\"rgba(255, 255, 255, 0.0)\"\r\n            strokeWidth=\"2\"\r\n        />\r\n        <circle\r\n            cx=\"25\"\r\n            cy=\"25\"\r\n            r=\"20\"\r\n            stroke=\"url(#fadeGradient)\"\r\n            strokeWidth=\"1\"\r\n            strokeDasharray=\"70\"\r\n            strokeDashoffset=\"10\"\r\n            fill=\"none\"\r\n        />\r\n        </svg>\r\n\r\n        {/* MACRA Logo */}\r\n        <Image\r\n        src=\"/images/macra-logo.png\"\r\n        alt=\"MACRA Logo\"\r\n        width={40}\r\n        height={40}\r\n        className=\"object-contain absolute inset-0 h-10 w-10 m-auto animate-fadeLoop\"\r\n        />\r\n    </div>\r\n\r\n    {/* Dynamic message */}\r\n    <p className=\"mt-4 text-gray-600\">{message}</p>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Loader\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,SAAS,CAAC,EAAE,UAAU,YAAY,EAAE;IACxC,qBACE,8OAAC;QAAI,WAAU;;0BACf,8OAAC;gBAAI,WAAU;;kCAEX,8OAAC;wBACD,WAAU;wBACV,SAAQ;wBACR,MAAK;;0CAEL,8OAAC;0CACG,cAAA,8OAAC;oCAAe,IAAG;oCAAe,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC/D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;4CAAU,aAAY;;;;;;sDAClD,8OAAC;4CAAK,QAAO;4CAAM,WAAU;4CAAU,aAAY;;;;;;sDACnD,8OAAC;4CAAK,QAAO;4CAAM,WAAU;4CAAU,aAAY;;;;;;sDACnD,8OAAC;4CAAK,QAAO;4CAAO,WAAU;4CAAU,aAAY;;;;;;;;;;;;;;;;;0CAIxD,8OAAC;gCACG,IAAG;gCACH,IAAG;gCACH,GAAE;gCACF,QAAO;gCACP,aAAY;;;;;;0CAEhB,8OAAC;gCACG,IAAG;gCACH,IAAG;gCACH,GAAE;gCACF,QAAO;gCACP,aAAY;gCACZ,iBAAgB;gCAChB,kBAAiB;gCACjB,MAAK;;;;;;;;;;;;kCAKT,8OAAC,6HAAA,CAAA,UAAK;wBACN,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;;0BAKd,8OAAC;gBAAE,WAAU;0BAAsB;;;;;;;;;;;;AAGvC;uCAEe", "debugId": null}}, {"offset": {"line": 1858, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/contexts/LoadingContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport { usePathname } from 'next/navigation';\r\nimport Loader from '@/components/Loader';\r\n\r\ninterface LoadingContextType {\r\n  isLoading: boolean;\r\n  setLoading: (loading: boolean) => void;\r\n  showLoader: (message?: string) => void;\r\n  hideLoader: () => void;\r\n}\r\n\r\nconst LoadingContext = createContext<LoadingContextType | undefined>(undefined);\r\n\r\nexport const useLoading = () => {\r\n  const context = useContext(LoadingContext);\r\n  if (!context) {\r\n    throw new Error('useLoading must be used within a LoadingProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface LoadingProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport const LoadingProvider: React.FC<LoadingProviderProps> = ({ children }) => {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [loadingMessage, setLoadingMessage] = useState('Loading...');\r\n  const pathname = usePathname();\r\n\r\n  // Handle route changes\r\n  useEffect(() => {\r\n    const handleStart = () => {\r\n      setIsLoading(true);\r\n      setLoadingMessage('Loading page...');\r\n    };\r\n\r\n    const handleComplete = () => {\r\n      // Add a small delay to ensure smooth transition\r\n      setTimeout(() => {\r\n        setIsLoading(false);\r\n      }, 300);\r\n    };\r\n\r\n    // Listen for route changes\r\n    handleStart();\r\n    handleComplete();\r\n  }, [pathname]);\r\n\r\n  const setLoading = (loading: boolean) => {\r\n    setIsLoading(loading);\r\n  };\r\n\r\n  const showLoader = (message: string = 'Loading...') => {\r\n    setLoadingMessage(message);\r\n    setIsLoading(true);\r\n  };\r\n\r\n  const hideLoader = () => {\r\n    setIsLoading(false);\r\n  };\r\n\r\n  return (\r\n    <LoadingContext.Provider value={{ isLoading, setLoading, showLoader, hideLoader }}>\r\n      {children}\r\n      {isLoading && (\r\n        <div className=\"fixed inset-0 bg-white dark:bg-gray-900 bg-opacity-80 dark:bg-opacity-80 backdrop-blur-sm z-50 flex items-center justify-center\">\r\n          <Loader message={loadingMessage} />\r\n        </div>\r\n      )}\r\n    </LoadingContext.Provider>\r\n  );\r\n};"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAaA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAkC;AAE9D,MAAM,aAAa;IACxB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,kBAAkD,CAAC,EAAE,QAAQ,EAAE;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,aAAa;YACb,kBAAkB;QACpB;QAEA,MAAM,iBAAiB;YACrB,gDAAgD;YAChD,WAAW;gBACT,aAAa;YACf,GAAG;QACL;QAEA,2BAA2B;QAC3B;QACA;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,aAAa,CAAC;QAClB,aAAa;IACf;IAEA,MAAM,aAAa,CAAC,UAAkB,YAAY;QAChD,kBAAkB;QAClB,aAAa;IACf;IAEA,MAAM,aAAa;QACjB,aAAa;IACf;IAEA,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;YAAE;YAAW;YAAY;YAAY;QAAW;;YAC7E;YACA,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;oBAAC,SAAS;;;;;;;;;;;;;;;;;AAK3B", "debugId": null}}, {"offset": {"line": 1945, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/lib/ThemeContext.js"], "sourcesContent": ["'use client';\r\n\r\nimport { createContext, useContext, useState, useEffect } from 'react';\r\n\r\nconst ThemeContext = createContext();\r\n\r\nexport function ThemeProvider({ children }) {\r\n  const [theme, setTheme] = useState('system');\r\n  const [resolvedTheme, setResolvedTheme] = useState('light');\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  // Set mounted to true after hydration\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Initialize theme from localStorage or system preference\r\n  useEffect(() => {\r\n    if (!mounted) return;\r\n\r\n    const savedTheme = localStorage.getItem('theme');\r\n    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {\r\n      setTheme(savedTheme);\r\n    } else {\r\n      setTheme('system');\r\n    }\r\n  }, [mounted]);\r\n\r\n  // Update resolved theme based on current theme setting\r\n  useEffect(() => {\r\n    if (!mounted) return;\r\n\r\n    const updateResolvedTheme = () => {\r\n      if (theme === 'system') {\r\n        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\r\n        setResolvedTheme(systemTheme);\r\n      } else {\r\n        setResolvedTheme(theme);\r\n      }\r\n    };\r\n\r\n    updateResolvedTheme();\r\n\r\n    // Listen for system theme changes when using system theme\r\n    if (theme === 'system') {\r\n      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\r\n      mediaQuery.addEventListener('change', updateResolvedTheme);\r\n      return () => mediaQuery.removeEventListener('change', updateResolvedTheme);\r\n    }\r\n  }, [theme, mounted]);\r\n\r\n  // Apply theme to document\r\n  useEffect(() => {\r\n    if (!mounted) return;\r\n\r\n    if (resolvedTheme === 'dark') {\r\n      document.documentElement.classList.add('dark');\r\n    } else {\r\n      document.documentElement.classList.remove('dark');\r\n    }\r\n  }, [resolvedTheme, mounted]);\r\n\r\n  const setThemePreference = (newTheme) => {\r\n    setTheme(newTheme);\r\n    if (mounted) {\r\n      localStorage.setItem('theme', newTheme);\r\n    }\r\n  };\r\n\r\n  const toggleTheme = () => {\r\n    const newTheme = resolvedTheme === 'light' ? 'dark' : 'light';\r\n    setThemePreference(newTheme);\r\n  };\r\n\r\n  // Prevent hydration mismatch by not rendering until mounted\r\n  if (!mounted) {\r\n    return (\r\n      <ThemeContext.Provider value={{\r\n        theme: 'light',\r\n        resolvedTheme: 'light',\r\n        setTheme: () => {},\r\n        toggleTheme: () => {}\r\n      }}>\r\n        {children}\r\n      </ThemeContext.Provider>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <ThemeContext.Provider value={{\r\n      theme,\r\n      resolvedTheme,\r\n      setTheme: setThemePreference,\r\n      toggleTheme\r\n    }}>\r\n      {children}\r\n    </ThemeContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useTheme() {\r\n  const context = useContext(ThemeContext);\r\n  if (!context) {\r\n    throw new Error('useTheme must be used within a ThemeProvider');\r\n  }\r\n  return context;\r\n}"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;AAE1B,SAAS,cAAc,EAAE,QAAQ,EAAE;IACxC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,cAAc;YAAC;YAAS;YAAQ;SAAS,CAAC,QAAQ,CAAC,aAAa;YAClE,SAAS;QACX,OAAO;YACL,SAAS;QACX;IACF,GAAG;QAAC;KAAQ;IAEZ,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,MAAM,sBAAsB;YAC1B,IAAI,UAAU,UAAU;gBACtB,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;gBACzF,iBAAiB;YACnB,OAAO;gBACL,iBAAiB;YACnB;QACF;QAEA;QAEA,0DAA0D;QAC1D,IAAI,UAAU,UAAU;YACtB,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,WAAW,gBAAgB,CAAC,UAAU;YACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;QACxD;IACF,GAAG;QAAC;QAAO;KAAQ;IAEnB,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,IAAI,kBAAkB,QAAQ;YAC5B,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;QACzC,OAAO;YACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;QAC5C;IACF,GAAG;QAAC;QAAe;KAAQ;IAE3B,MAAM,qBAAqB,CAAC;QAC1B,SAAS;QACT,IAAI,SAAS;YACX,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,WAAW,kBAAkB,UAAU,SAAS;QACtD,mBAAmB;IACrB;IAEA,4DAA4D;IAC5D,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC,aAAa,QAAQ;YAAC,OAAO;gBAC5B,OAAO;gBACP,eAAe;gBACf,UAAU,KAAO;gBACjB,aAAa,KAAO;YACtB;sBACG;;;;;;IAGP;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAC5B;YACA;YACA,UAAU;YACV;QACF;kBACG;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2064, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/ui/Toast.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\n\r\nexport interface ToastProps {\r\n  message: string;\r\n  type: 'success' | 'error' | 'warning' | 'info';\r\n  duration?: number;\r\n  onClose: () => void;\r\n}\r\n\r\nconst Toast: React.FC<ToastProps> = ({ message, type, duration = 5000, onClose }) => {\r\n  const [isVisible, setIsVisible] = useState(true);\r\n\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setIsVisible(false);\r\n      setTimeout(onClose, 300); // Wait for fade out animation\r\n    }, duration);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [duration, onClose]);\r\n\r\n  const getToastStyles = () => {\r\n    switch (type) {\r\n      case 'success':\r\n        return 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-300';\r\n      case 'error':\r\n        return 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-300';\r\n      case 'warning':\r\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-300';\r\n      case 'info':\r\n        return 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-300';\r\n      default:\r\n        return 'bg-gray-50 border-gray-200 text-gray-800 dark:bg-gray-900/20 dark:border-gray-800 dark:text-gray-300';\r\n    }\r\n  };\r\n\r\n  const getIcon = () => {\r\n    switch (type) {\r\n      case 'success':\r\n        return 'ri-check-circle-line';\r\n      case 'error':\r\n        return 'ri-error-warning-line';\r\n      case 'warning':\r\n        return 'ri-alert-line';\r\n      case 'info':\r\n        return 'ri-information-line';\r\n      default:\r\n        return 'ri-information-line';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`relative max-w-sm w-full border rounded-lg p-4 shadow-lg transition-all duration-300 ${\r\n        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-2'\r\n      } ${getToastStyles()}`}\r\n    >\r\n      <div className=\"flex items-start\">\r\n        <div className=\"flex-shrink-0\">\r\n          <i className={`${getIcon()} text-lg`}></i>\r\n        </div>\r\n        <div className=\"ml-3 flex-1\">\r\n          <p className=\"text-sm font-medium\">{message}</p>\r\n        </div>\r\n        <div className=\"ml-4 flex-shrink-0\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => {\r\n              setIsVisible(false);\r\n              setTimeout(onClose, 300);\r\n            }}\r\n            className=\"inline-flex rounded-md p-1.5 hover:bg-black/5 dark:hover:bg-white/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current\"\r\n          >\r\n            <span className=\"sr-only\">Dismiss</span>\r\n            <i className=\"ri-close-line text-sm\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Toast;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAWA,MAAM,QAA8B,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,IAAI,EAAE,OAAO,EAAE;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,aAAa;YACb,WAAW,SAAS,MAAM,8BAA8B;QAC1D,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,qFAAqF,EAC/F,YAAY,8BAA8B,2BAC3C,CAAC,EAAE,kBAAkB;kBAEtB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAW,GAAG,UAAU,QAAQ,CAAC;;;;;;;;;;;8BAEtC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAuB;;;;;;;;;;;8BAEtC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,MAAK;wBACL,SAAS;4BACP,aAAa;4BACb,WAAW,SAAS;wBACtB;wBACA,WAAU;;0CAEV,8OAAC;gCAAK,WAAU;0CAAU;;;;;;0CAC1B,8OAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzB;uCAEe", "debugId": null}}, {"offset": {"line": 2199, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/contexts/ToastContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, ReactNode } from 'react';\r\nimport Toast, { ToastProps } from '@/components/ui/Toast';\r\nimport '@/styles/toast.css';\r\n\r\ninterface ToastContextType {\r\n  showToast: (message: string, type: ToastProps['type'], duration?: number) => void;\r\n  showSuccess: (message: string, duration?: number) => void;\r\n  showError: (message: string, duration?: number) => void;\r\n  showWarning: (message: string, duration?: number) => void;\r\n  showInfo: (message: string, duration?: number) => void;\r\n}\r\n\r\nconst ToastContext = createContext<ToastContextType | undefined>(undefined);\r\n\r\ninterface ToastItem {\r\n  id: string;\r\n  message: string;\r\n  type: ToastProps['type'];\r\n  duration?: number;\r\n}\r\n\r\ninterface ToastProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {\r\n  const [toasts, setToasts] = useState<ToastItem[]>([]);\r\n\r\n  const showToast = (message: string, type: ToastProps['type'], duration?: number) => {\r\n    const id = Math.random().toString(36).substr(2, 9);\r\n    const newToast: ToastItem = { id, message, type, duration };\r\n    \r\n    setToasts(prev => [...prev, newToast]);\r\n  };\r\n\r\n  const removeToast = (id: string) => {\r\n    setToasts(prev => prev.filter(toast => toast.id !== id));\r\n  };\r\n\r\n  const showSuccess = (message: string, duration?: number) => {\r\n    showToast(message, 'success', duration);\r\n  };\r\n\r\n  const showError = (message: string, duration?: number) => {\r\n    showToast(message, 'error', duration);\r\n  };\r\n\r\n  const showWarning = (message: string, duration?: number) => {\r\n    showToast(message, 'warning', duration);\r\n  };\r\n\r\n  const showInfo = (message: string, duration?: number) => {\r\n    showToast(message, 'info', duration);\r\n  };\r\n\r\n  return (\r\n    <ToastContext.Provider value={{\r\n      showToast,\r\n      showSuccess,\r\n      showError,\r\n      showWarning,\r\n      showInfo\r\n    }}>\r\n      {children}\r\n      \r\n      {/* Render toasts */}\r\n      <div className=\"toast-container\">\r\n        {toasts.map((toast, index) => (\r\n          <div\r\n            key={toast.id}\r\n            className=\"toast-wrapper\"\r\n            data-index={index}\r\n            data-toast-index={index}\r\n          >\r\n            <Toast\r\n              message={toast.message}\r\n              type={toast.type}\r\n              duration={toast.duration}\r\n              onClose={() => removeToast(toast.id)}\r\n            />\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </ToastContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useToast = (): ToastContextType => {\r\n  const context = useContext(ToastContext);\r\n  if (!context) {\r\n    throw new Error('useToast must be used within a ToastProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;;AAcA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAa1D,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;IACtE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAEpD,MAAM,YAAY,CAAC,SAAiB,MAA0B;QAC5D,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,MAAM,WAAsB;YAAE;YAAI;YAAS;YAAM;QAAS;QAE1D,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;IACvC;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,cAAc,CAAC,SAAiB;QACpC,UAAU,SAAS,WAAW;IAChC;IAEA,MAAM,YAAY,CAAC,SAAiB;QAClC,UAAU,SAAS,SAAS;IAC9B;IAEA,MAAM,cAAc,CAAC,SAAiB;QACpC,UAAU,SAAS,WAAW;IAChC;IAEA,MAAM,WAAW,CAAC,SAAiB;QACjC,UAAU,SAAS,QAAQ;IAC7B;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAC5B;YACA;YACA;YACA;YACA;QACF;;YACG;0BAGD,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;wBAEC,WAAU;wBACV,cAAY;wBACZ,oBAAkB;kCAElB,cAAA,8OAAC,iIAAA,CAAA,UAAK;4BACJ,SAAS,MAAM,OAAO;4BACtB,MAAM,MAAM,IAAI;4BAChB,UAAU,MAAM,QAAQ;4BACxB,SAAS,IAAM,YAAY,MAAM,EAAE;;;;;;uBAThC,MAAM,EAAE;;;;;;;;;;;;;;;;AAgBzB;AAEO,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2296, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/NoSSR.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ReactNode, useEffect, useState } from 'react';\r\n\r\ninterface NoSSRProps {\r\n  children: ReactNode;\r\n  fallback?: ReactNode;\r\n}\r\n\r\nexport default function NoSSR({ children, fallback = null }: NoSSRProps) {\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  if (!mounted) {\r\n    return <>{fallback}</>;\r\n  }\r\n\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,MAAM,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAc;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 2322, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/ClientWrapper.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ReactNode } from 'react';\r\nimport { AuthProvider } from '../contexts/AuthContext';\r\nimport { LoadingProvider } from '../contexts/LoadingContext';\r\nimport { ThemeProvider } from '../lib/ThemeContext';\r\nimport { ToastProvider } from '../contexts/ToastContext';\r\nimport Loader from './Loader';\r\nimport NoSSR from './NoSSR';\r\n\r\ninterface ClientWrapperProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport default function ClientWrapper({ children }: ClientWrapperProps) {\r\n  return (\r\n    <NoSSR fallback={\r\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\r\n        <Loader message=\"Initializing application...\" />\r\n      </div>\r\n    }>\r\n      <ThemeProvider>\r\n        <LoadingProvider>\r\n          <ToastProvider>\r\n            <AuthProvider>\r\n              {children}\r\n            </AuthProvider>\r\n          </ToastProvider>\r\n        </LoadingProvider>\r\n      </ThemeProvider>\r\n    </NoSSR>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAce,SAAS,cAAc,EAAE,QAAQ,EAAsB;IACpE,qBACE,8OAAC,2HAAA,CAAA,UAAK;QAAC,wBACL,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;gBAAC,SAAQ;;;;;;;;;;;kBAGlB,cAAA,8OAAC,0HAAA,CAAA,gBAAa;sBACZ,cAAA,8OAAC,kIAAA,CAAA,kBAAe;0BACd,cAAA,8OAAC,gIAAA,CAAA,gBAAa;8BACZ,cAAA,8OAAC,+HAAA,CAAA,eAAY;kCACV;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 2391, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LoadingOptimizer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect } from 'react';\r\n\r\ninterface LoadingOptimizerProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nconst LoadingOptimizer: React.FC<LoadingOptimizerProps> = ({ children }) => {\r\n  useEffect(() => {\r\n    // Optimize images with lazy loading\r\n    const optimizeImages = () => {\r\n      const images = document.querySelectorAll('img[data-src]');\r\n      const imageObserver = new IntersectionObserver((entries) => {\r\n        entries.forEach((entry) => {\r\n          if (entry.isIntersecting) {\r\n            const img = entry.target as HTMLImageElement;\r\n            img.src = img.dataset.src || '';\r\n            img.classList.remove('lazy');\r\n            imageObserver.unobserve(img);\r\n          }\r\n        });\r\n      });\r\n\r\n      images.forEach((img) => imageObserver.observe(img));\r\n    };\r\n\r\n    // Optimize page performance\r\n    const optimizePerformance = () => {\r\n      // Add will-change to elements that will animate\r\n      const animatedElements = document.querySelectorAll('.nav-item, .dashboard-card, .btn-primary');\r\n      animatedElements.forEach((el) => {\r\n        (el as HTMLElement).style.willChange = 'transform, background-color';\r\n      });\r\n\r\n      // Optimize scroll performance\r\n      document.documentElement.style.scrollBehavior = 'smooth';\r\n    };\r\n\r\n    // Run optimizations after DOM is ready\r\n    const timer = setTimeout(() => {\r\n      optimizeImages();\r\n      optimizePerformance();\r\n    }, 100);\r\n\r\n    return () => {\r\n      clearTimeout(timer);\r\n      // Cleanup will-change properties\r\n      const animatedElements = document.querySelectorAll('.nav-item, .dashboard-card, .btn-primary');\r\n      animatedElements.forEach((el) => {\r\n        (el as HTMLElement).style.willChange = 'auto';\r\n      });\r\n    };\r\n  }, []);\r\n\r\n  return <>{children}</>;\r\n};\r\n\r\nexport default LoadingOptimizer;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQA,MAAM,mBAAoD,CAAC,EAAE,QAAQ,EAAE;IACrE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oCAAoC;QACpC,MAAM,iBAAiB;YACrB,MAAM,SAAS,SAAS,gBAAgB,CAAC;YACzC,MAAM,gBAAgB,IAAI,qBAAqB,CAAC;gBAC9C,QAAQ,OAAO,CAAC,CAAC;oBACf,IAAI,MAAM,cAAc,EAAE;wBACxB,MAAM,MAAM,MAAM,MAAM;wBACxB,IAAI,GAAG,GAAG,IAAI,OAAO,CAAC,GAAG,IAAI;wBAC7B,IAAI,SAAS,CAAC,MAAM,CAAC;wBACrB,cAAc,SAAS,CAAC;oBAC1B;gBACF;YACF;YAEA,OAAO,OAAO,CAAC,CAAC,MAAQ,cAAc,OAAO,CAAC;QAChD;QAEA,4BAA4B;QAC5B,MAAM,sBAAsB;YAC1B,gDAAgD;YAChD,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;YACnD,iBAAiB,OAAO,CAAC,CAAC;gBACvB,GAAmB,KAAK,CAAC,UAAU,GAAG;YACzC;YAEA,8BAA8B;YAC9B,SAAS,eAAe,CAAC,KAAK,CAAC,cAAc,GAAG;QAClD;QAEA,uCAAuC;QACvC,MAAM,QAAQ,WAAW;YACvB;YACA;QACF,GAAG;QAEH,OAAO;YACL,aAAa;YACb,iCAAiC;YACjC,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;YACnD,iBAAiB,OAAO,CAAC,CAAC;gBACvB,GAAmB,KAAK,CAAC,UAAU,GAAG;YACzC;QACF;IACF,GAAG,EAAE;IAEL,qBAAO;kBAAG;;AACZ;uCAEe", "debugId": null}}]}